# LVGL RTOS app target (moved from src/)

# Target name matches the primary firmware
set(APP_TARGET hs)

# Sources: app main and startup; the rest comes from hs_core library
set(APP_SOURCES
  ${CMAKE_SOURCE_DIR}/src/startup_gcc.c
  ${CMAKE_CURRENT_SOURCE_DIR}/main.c
)

add_executable(${APP_TARGET} ${APP_SOURCES})

set_target_properties(${APP_TARGET} PROPERTIES
  OUTPUT_NAME ${APP_TARGET}
  SUFFIX ".axf"
)

# Includes
# - src for shared headers
# - this app dir for its own headers
# - external includes via ambiqsuite_includes target
# Other external include paths are handled by linked libraries
 target_include_directories(${APP_TARGET} PRIVATE
  ${CMAKE_SOURCE_DIR}/src
  ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link directories for vendor/gpu libs
 target_link_directories(${APP_TARGET} PRIVATE
  ${AMBIQSUITE_DIR}/bsp/gcc/bin
  ${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu/gcc/bin
  ${AMBIQSUITE_DIR}/ARM/Lib/ARM
  ${LVGL_DIR}/ambiq_support/gpu_lib_apollo4/gcc/bin
  ${THINKSI_DIR}/config/apollo4p_nemagfx/gcc/bin
)

# Link with core and external libraries (order matters for static libs)
# - lvgl and ThinkSi libs need symbols from hs_core (nema_*), so list them BEFORE hs_core
# - hs_core needs symbols from am_bsp/am_hal/freertos, so list those AFTER hs_core
 target_link_libraries(${APP_TARGET} PRIVATE
  -Wl,--start-group
  ambiqsuite_includes
  lvgl
  ${LVGL_DIR}/ambiq_support/gpu_lib_apollo4/gcc/bin/lvgl_ambiq_porting.a
  ${THINKSI_DIR}/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a
  hs_core
  lvgl_ambiq_support
  am_bsp
  am_hal
  ${AMBIQSUITE_DIR}/ARM/Lib/ARM/libarm_cortexM4lf_math.a
  freertos
  m c gcc nosys
  -Wl,--end-group
)

# Ensure vendor libs prebuilt
add_dependencies(${APP_TARGET} vendor_libs)

# Linker script and link options
 target_link_options(${APP_TARGET} PRIVATE
  -T${CMAKE_SOURCE_DIR}/src/linker_script.ld
  -Wl,--gc-sections
  -Wl,--entry,Reset_Handler
  -Wl,-Map,${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${APP_TARGET}.map
)

# Post-build artifacts
 add_custom_command(TARGET ${APP_TARGET} POST_BUILD
  COMMAND ${CMAKE_OBJCOPY} -Obinary $<TARGET_FILE:${APP_TARGET}> ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${APP_TARGET}.bin
  COMMAND ${CMAKE_OBJDUMP} -S $<TARGET_FILE:${APP_TARGET}> > ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${APP_TARGET}.lst
  COMMAND ${CMAKE_SIZE} $<TARGET_FILE:${APP_TARGET}> > ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${APP_TARGET}.size
  COMMENT "Creating binary and listing files"
)

