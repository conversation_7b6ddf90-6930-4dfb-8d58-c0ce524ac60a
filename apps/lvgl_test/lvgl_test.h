#ifndef LVGL_TEST_H
#define LVGL_TEST_H

#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

#include "am_bsp.h"
#include "am_mcu_apollo.h"
#include "am_util.h"

#include "lvgl.h"

#ifdef SYSTEM_VIEW
#include "SEGGER_SYSVIEW_FreeRTOS.h"
#endif
#include "FreeRTOS.h"
#include "event_groups.h"
#include "portable.h"
#include "portmacro.h"
#include "semphr.h"
#include "task.h"
#ifdef SYSTEM_VIEW
#include "SEGGER_SYSVIEW.h"
#endif

#include "gui_task.h"
#include "board_init.h"
#include "minimal_display_task.h"
#include "nema_core.h"
#include "nema_event.h"
#include "nema_graphics.h"
#include "nema_hal.h"
#include "nema_math.h"
#include "nema_programHW.h"
#include "nema_regs.h"
#include "nema_utils.h"
#include "rtos.h"

#if defined(AM_PART_APOLLO4L) || defined(AM_PART_APOLLO4P)
#include "am_devices_mspi_psram_aps25616n.h"
#elif defined(AM_PART_APOLLO4B)
#include "am_devices_mspi_psram_aps12808l.h"
#endif

#define MSPI_PSRAM_MODULE 0
#if (MSPI_PSRAM_MODULE == 0)
#define MSPI_XIP_BASE_ADDRESS (MSPI0_APERTURE_START_ADDR)
#elif (MSPI_PSRAM_MODULE == 1)
#define MSPI_XIP_BASE_ADDRESS (MSPI1_APERTURE_START_ADDR)
#elif (MSPI_PSRAM_MODULE == 2)
#define MSPI_XIP_BASE_ADDRESS (MSPI2_APERTURE_START_ADDR)
#endif
#define MSPI_PSRAM_SIZE (0x800000)

#define TASK_PRIORITY_LOW (4)
#define TASK_PRIORITY_MIDDLE (TASK_PRIORITY_LOW + 1)
#define TASK_PRIORITY_HIGH (TASK_PRIORITY_MIDDLE + 1)
#define TASK_PRIORITY_HIGHEST (TASK_PRIORITY_MIDDLE + 2)

#define PSRAM_ISR_PRIORITY (NVIC_configMAX_SYSCALL_INTERRUPT_PRIORITY + 2)

#endif // LVGL_TEST_H

