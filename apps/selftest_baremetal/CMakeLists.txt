# Bare-metal board self-test firmware target

# Target name
set(SELFTEST_TARGET selftest)

# Minimal preprocessor definitions (no FreeRTOS/LVGL)
set(SELFTEST_DEFINES
    PART_apollo4p
    AM_PACKAGE_BGA
    AM_PART_APOLLO4P
    AM_UTIL_FAULTISR_PRINT
    gcc
)

# Collect sources from src/ for bare-metal bring-up
# Start with all .c files in src, then remove RTOS/LVGL app demos we don't want
file(GLOB SELFTEST_SRC_ALL
    ${CMAKE_SOURCE_DIR}/src/*.c
)

# Exclude application sources specific to the main RTOS/LVGL firmware
# and optional utilities we do not need in bare-metal selftest
list(REMOVE_ITEM SELFTEST_SRC_ALL
    ${CMAKE_SOURCE_DIR}/src/lvgl_test.c
    ${CMAKE_SOURCE_DIR}/src/minimal_display_task.c
    ${CMAKE_SOURCE_DIR}/src/rtos.c
    ${CMAKE_SOURCE_DIR}/src/gui_task.c
    ${CMAKE_SOURCE_DIR}/src/cli_shell.c
    # ${CMAKE_SOURCE_DIR}/src/SEGGER_RTT.c
    # ${CMAKE_SOURCE_DIR}/src/SEGGER_RTT_printf.c
    # ${CMAKE_SOURCE_DIR}/src/SEGGER_RTT_ASM_ARMv7M.S
    ${CMAKE_SOURCE_DIR}/src/my_watch.c
)

# Add AmbiqSuite utils needed for printf/delay/etc.
list(APPEND SELFTEST_SRC_ALL
    ${AMBIQSUITE_DIR}/utils/am_util_stdio.c
    ${AMBIQSUITE_DIR}/utils/am_util_delay.c
    ${AMBIQSUITE_DIR}/utils/am_util_id.c
    ${AMBIQSUITE_DIR}/utils/am_util_string.c
    ${AMBIQSUITE_DIR}/utils/am_util_debug.c
    ${AMBIQSUITE_DIR}/utils/am_util_faultisr.c
    ${AMBIQSUITE_DIR}/utils/am_util_syscalls.c
)

# Add the bare-metal entry point (renamed main)
list(APPEND SELFTEST_SRC_ALL
    ${CMAKE_CURRENT_SOURCE_DIR}/main.c
)

# Create executable
add_executable(${SELFTEST_TARGET} ${SELFTEST_SRC_ALL})

set_target_properties(${SELFTEST_TARGET} PROPERTIES
    OUTPUT_NAME ${SELFTEST_TARGET}
    SUFFIX ".axf"
)

# Includes
# - src includes for project headers
# - External include aggregation via ambiqsuite_includes (from external/)
target_include_directories(${SELFTEST_TARGET} PRIVATE
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/src/include
    ${AMBIQSUITE_DIR}/utils
)

# Compile definitions
target_compile_definitions(${SELFTEST_TARGET} PRIVATE ${SELFTEST_DEFINES})

# Link search paths (vendor prebuilt libs)
target_link_directories(${SELFTEST_TARGET} PRIVATE
  ${AMBIQSUITE_DIR}/bsp/gcc/bin
  ${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu/gcc/bin
  ${AMBIQSUITE_DIR}/ARM/Lib/ARM
)

# Link libraries (no LVGL/FreeRTOS for bare-metal test)
target_link_libraries(${SELFTEST_TARGET} PRIVATE
  ambiqsuite_includes
  am_bsp
  am_hal
  m
  ${AMBIQSUITE_DIR}/ARM/Lib/ARM/libarm_cortexM4lf_math.a
)

# Ensure vendor libs are built first
add_dependencies(${SELFTEST_TARGET} vendor_libs)

# Linker script and link options
# Reuse the existing startup/linker from src
# Note: startup_gcc.c is included from src; ensure only one main() exists (in main.c here)
target_link_options(${SELFTEST_TARGET} PRIVATE
    -T${CMAKE_SOURCE_DIR}/src/linker_script.ld
    -Wl,--gc-sections
    -Wl,--entry,Reset_Handler
    -Wl,-Map,${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${SELFTEST_TARGET}.map
)

# Post-build artifacts: bin/lst/size
add_custom_command(TARGET ${SELFTEST_TARGET} POST_BUILD
    COMMAND ${CMAKE_OBJCOPY} -Obinary $<TARGET_FILE:${SELFTEST_TARGET}> ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${SELFTEST_TARGET}.bin
    COMMAND ${CMAKE_OBJDUMP} -S $<TARGET_FILE:${SELFTEST_TARGET}> > ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${SELFTEST_TARGET}.lst
    COMMAND ${CMAKE_SIZE} $<TARGET_FILE:${SELFTEST_TARGET}> > ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${SELFTEST_TARGET}.size
    COMMENT "Creating binary and listing files"
)

