// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the use
//   or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
// Bare-metal self-test entry point merged from src/my_watch.c
//
#include <stdbool.h>
#include <string.h>

#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"
#include "am_hal_global.h"
#include "am_hal_gpio.h"
#include "am_hal_mspi.h"

#include "storage_config.h"

#include "board_init.h"

#include "i2c.h"
#include "motor.h"
#include "flash.h"
#include "adc.h"
#include "six_d_sensor.h"
#include "sh8601_example.h"
#include "sip1221.h"
#include "uart.h"

#define ENABLE_DEBUGGER

#ifndef AM_BSP_PWM_LED_TIMER
#define AM_BSP_PWM_LED_TIMER 0
#endif

#ifndef AM_BSP_GPIO_LED0
#define AM_BSP_GPIO_LED0 0x10000
#endif
#ifndef AM_BSP_GPIO_LED1
#define AM_BSP_GPIO_LED1 0x10000
#endif

static inline uint32_t stimer_now(void)
{
    return am_hal_stimer_counter_get();
}

static float stimer_elapsed_ms(uint32_t t0, uint32_t t1)
{
    uint32_t dt_ticks = (t1 >= t0) ? (t1 - t0) : (0xFFFFFFFF - t0 + t1 + 1);

    // 6MHz HFRC: 6000 ticks = 1 ms 
    return (dt_ticks / 6000.0f);
}

static bool buffer_all_ff(const uint8_t *buf, size_t len)
{
    bool all_ff = true;
    for (size_t i = 0; i < len; ++i)
    {
        if (buf[i] != 0xFF)
        {
            am_util_stdio_printf("Buffer not all 0xFF at index %lu\r\n with value %02x\r\n", (unsigned long)i, buf[i]);
            all_ff = false;
        }
    }
    return all_ff;
}

int main(void)
{
    am_util_id_t sIdDevice;
    uint32_t ui32StrBuf;
    am_hal_reset_status_t sResetStatus;
    am_hal_security_info_t secInfo;
    uint32_t ui32Ret;
    uint32_t ui32TrimVer;
    char sINFO[32];

    uint32_t led_flag=0;

    uint8_t I2C_read=0;
    uint8_t i=0;
    uint32_t counter=0;

    uint32_t frame_counter = 0;

    static uint16_t test_pattern[200*200];
    memset(test_pattern, 0xAAAA, sizeof(test_pattern));

    board_init(); 

    am_util_stdio_printf("Initializing the LRA motor...\r\n");
    am_util_delay_ms(10);

    drv2605_init_lra();
    drv2605_play_effect();

    am_util_stdio_printf("The LRA motor Initialized!\r\n");

    am_util_stdio_printf("Starting test the NAND FLASH...\r\n");

    mspi2_init_spi_mode();
    uint8_t id[6] = {0};
    mspi2_read(0x9F, 0x00, id, 2);

    am_util_stdio_printf("FLASH Manufacturer ID %x\r\n",id[0]);
    am_util_stdio_printf("FLASH Device ID %x\r\n",id[1]);

    am_util_stdio_printf("ID[0] %x\r\n",id[0]);
    am_util_stdio_printf("ID[1] %x\r\n",id[1]);

    am_util_stdio_printf("Selecting NAND block for self-test...\r\n");
    uint8_t page_probe[NAND_PAGE_SIZE + NAND_OOB_SIZE];
    uint32_t selected_block = UINT32_MAX;
    uint32_t nonblank_blocks = 0;
    uint32_t max_probe_blocks = (NAND_BLOCKS < 16u) ? NAND_BLOCKS : 16u;
    for (uint32_t block = 1; block < max_probe_blocks; ++block)
    {
        uint32_t row = block * NAND_PAGES_PER_BLOCK;
        if (flash_read_page(row, 0, page_probe, sizeof(page_probe)) == 0 &&
            buffer_all_ff(page_probe, sizeof(page_probe)))
        {
            selected_block = block;
            break;
        }

        ++nonblank_blocks;
    }


    // leo: note that the selected block will naturally grow as a result of
    // writes from previous test runs. We should eventually move away from this
    // raw NAND write/read tests, and test via the filesystem instead.  
    if (selected_block == UINT32_MAX)
    {
        am_util_stdio_printf("Unable to find blank block (non-blank count: %lu); skipping NAND program/verify.\r\n",
                              (unsigned long)nonblank_blocks);
    }
    else
    {
        am_util_stdio_printf("Skipped %lu non-blank blocks before selecting block %lu for NAND self-test.\r\n",
                              (unsigned long)nonblank_blocks, (unsigned long)selected_block);

        uint32_t test_page_row = selected_block * NAND_PAGES_PER_BLOCK;
        uint16_t test_column = 0;
        am_util_stdio_printf("Using block %lu (row %lu) for NAND self-test.\r\n",
                              (unsigned long)selected_block, (unsigned long)test_page_row);

        uint8_t prot_val = 0;
        if (flash_get_feature(NAND_FEATURE_PROTECTION, &prot_val) == 0)
        {
            am_util_stdio_printf("Block lock register is 0x%02X\r\n", prot_val);
        }

        if (flash_set_feature(NAND_FEATURE_PROTECTION, NAND_PROTECTION_ALL_UNLOCKED) == 0)
        {
            am_util_stdio_printf("Issued block unlock command.\r\n");
            if (flash_get_feature(NAND_FEATURE_PROTECTION, &prot_val) == 0)
            {
                am_util_stdio_printf("Block lock register now 0x%02X\r\n", prot_val);
            }
        }
        else
        {
            am_util_stdio_printf("Failed to write block lock register.\r\n");
        }

        uint8_t program_buf[16];
        uint8_t verify_buf[sizeof(program_buf)];
        memset(verify_buf, 0, sizeof(verify_buf));
        for (uint32_t n = 0; n < sizeof(program_buf); ++n)
        {
            program_buf[n] = (uint8_t)(0xA0 + n);
        }

        int prog_rc = flash_test_program_page(test_page_row, test_column, program_buf, sizeof(program_buf));
        if (prog_rc != 0)
        {
            am_util_stdio_printf("flash_test_program_page failed (%d)\r\n", prog_rc);
        }
        else
        {
            bool verify_ok = false;
            uint8_t status_byte = 0;

            if (flash_read_page(test_page_row, test_column, verify_buf, sizeof(verify_buf)) != 0)
            {
                am_util_stdio_printf("flash_read_page failed\r\n");
            }
            else if (memcmp(program_buf, verify_buf, sizeof(program_buf)) != 0)
            {
                am_util_stdio_printf("NAND verify mismatch\r\n");
            }
            else
            {
                verify_ok = true;
                am_util_stdio_printf("NAND program/verify test passed\r\n");
            }

            if (!verify_ok)
            {
                if (flash_get_feature(NAND_FEATURE_ADDR_STATUS, &status_byte) == 0)
                {
                    am_util_stdio_printf("Final NAND status: 0x%02x\r\n", status_byte);
                }
                am_util_stdio_printf("NAND program/verify test failed\r\n");
            }
        }
    }

    am_util_stdio_printf("Test MIC...\r\n");

#if defined(AM_PART_APOLLO4P) && defined(DC_OFFSET_CAL)
    am_hal_offset_cal_coeffs_array_t sOffsetCalib;
    #if AUDADC_EXAMPLE_DEBUG
        int32_t i32OffsetAdj = 0;
        uint16_t ui16AudChannel = 0;
    #endif
#endif

#if defined(AM_PART_APOLLO4B)
    am_hal_daxi_control(AM_HAL_DAXI_CONTROL_AXIMEM, (uint8_t *)((uint32_t)(axiScratchBuf + 3) & ~0xF));
#endif 

    uint32_t ui32AUDADCDataPtr = (uint32_t)((uint32_t)(g_ui32AUDADCSampleBuffer + 3) & ~0xF);
    g_sAUDADCDMAConfig.ui32TargetAddress = ui32AUDADCDataPtr;
    g_sAUDADCDMAConfig.ui32TargetAddressReverse = g_sAUDADCDMAConfig.ui32TargetAddress + sizeof(uint32_t)* g_sAUDADCDMAConfig.ui32SampleCount;

    am_hal_audadc_refgen_powerup();

    am_hal_audadc_pga_powerup(0);
    am_hal_audadc_pga_powerup(1);
    am_hal_audadc_pga_powerup(2);
    am_hal_audadc_pga_powerup(3);

    am_hal_audadc_gain_set(0, 2*PREAMP_FULL_GAIN);
    am_hal_audadc_gain_set(1, 2*PREAMP_FULL_GAIN);
    am_hal_audadc_gain_set(2, 2*PREAMP_FULL_GAIN);
    am_hal_audadc_gain_set(3, 2*PREAMP_FULL_GAIN);

    am_hal_audadc_micbias_powerup(24);
    am_util_delay_ms(400);

    audadc_config();

    g_sAudadcGainConfig.ui32LGA = (uint32_t)((float)CH_A0_GAIN_DB*2 + 12);
    g_sAudadcGainConfig.ui32HGADELTA = ((uint32_t)((float)CH_A1_GAIN_DB*2 + 12)) - g_sAudadcGainConfig.ui32LGA;
    g_sAudadcGainConfig.ui32LGB = (uint32_t)((float)CH_B0_GAIN_DB*2 + 12);
    g_sAudadcGainConfig.ui32HGBDELTA = ((uint32_t)((float)CH_B1_GAIN_DB*2 + 12)) - g_sAudadcGainConfig.ui32LGB;
    g_sAudadcGainConfig.eUpdateMode = AM_HAL_AUDADC_GAIN_UPDATE_IMME;
    am_hal_audadc_internal_pga_config(g_AUDADCHandle, &g_sAudadcGainConfig);

    audadc_slot_config();
#if defined(AM_PART_APOLLO4P) && defined(DC_OFFSET_CAL)
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_slot_dc_offset_calculate(g_AUDADCHandle, 4, &sOffsetCalib))
    {
        am_util_stdio_printf("Error - failed to calculate offset calibartion parameter.\n");
    }
#endif
    NVIC_SetPriority(AUDADC0_IRQn, AM_IRQ_PRIORITY_DEFAULT);
    NVIC_EnableIRQ(AUDADC0_IRQn);
    am_hal_interrupt_master_enable();

    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_sw_trigger(g_AUDADCHandle))
    {
        am_util_stdio_printf("Error - triggering the AUDADC failed.\n");
    }
    while(1)
    {
        if (g_bAUDADCDMAError)
        {
            am_util_stdio_printf("DMA Error occured\n");
            while (1);
        }
        if (g_bAUDADCDMAComplete)
        {
            uint32_t* pui32_buffer = (uint32_t* )am_hal_audadc_dma_get_buffer(g_AUDADCHandle);
#if AUDADC_EXAMPLE_DEBUG
            uint32_t  ui32SampleCount = AUDADC_SAMPLE_BUF_SIZE;
            for (int i = 0; i < ui32SampleCount; i++)
            {
                g_in16AudioDataBuffer[2 * i]     = (int16_t)(pui32_buffer[i] & 0x0000FFF0);
                g_in16AudioDataBuffer[2 * i + 1] = (int16_t)((pui32_buffer[i] >> 16) & 0x0000FFF0);
#if defined(AM_PART_APOLLO4P) && defined(DC_OFFSET_CAL)
                if (sOffsetCalib.sCalibCoeff[ui16AudChannel * 2].bValid)
                {
                    i32OffsetAdj = sOffsetCalib.sCalibCoeff[ui16AudChannel * 2].i32DCOffsetAdj << 4;
                    if ((g_in16AudioDataBuffer[2 * i] >= 0) &&
                        (i32OffsetAdj > (32767 - g_in16AudioDataBuffer[2 * i])))
                    {
                        g_in16AudioDataBuffer[2 * i] = 32767;
                    }
                    else if ((g_in16AudioDataBuffer[2 * i] < 0) &&
                             (i32OffsetAdj < (-32768 - g_in16AudioDataBuffer[2 * i])))
                    {
                        g_in16AudioDataBuffer[2 * i] = -32768;
                    }
                    else
                    {
                        g_in16AudioDataBuffer[2 * i] += i32OffsetAdj;
                    }
                }
                if (sOffsetCalib.sCalibCoeff[ui16AudChannel * 2 + 1].bValid)
                {
                    i32OffsetAdj = sOffsetCalib.sCalibCoeff[ui16AudChannel * 2 + 1].i32DCOffsetAdj << 4;
                    if ((g_in16AudioDataBuffer[2 * i + 1] >= 0) &&
                        (i32OffsetAdj > (32767 - g_in16AudioDataBuffer[2 * i + 1])))
                    {
                        g_in16AudioDataBuffer[2 * i + 1] = 32767;
                    }
                    else if ((g_in16AudioDataBuffer[2 * i + 1] < 0) &&
                             (i32OffsetAdj < (-32768 - g_in16AudioDataBuffer[2 * i + 1])))
                    {
                        g_in16AudioDataBuffer[2 * i + 1] = -32768;
                    }
                    else
                    {
                        g_in16AudioDataBuffer[2 * i + 1] += i32OffsetAdj;
                    }
                }
#endif
            }
#endif
#if FIFO_READ
            uint32_t ui32SampleCount2 = AUDADC_SAMPLE_BUF_SIZE;
#if defined(AM_PART_APOLLO4P) && defined(DC_OFFSET_CAL)
            am_hal_audadc_samples_read(g_AUDADCHandle,
                                       NULL,
                                       &ui32SampleCount2,
                                       true, &sLGSampleBuffer[0],
                                       true, &sHGSampleBuffer[0],
                                       &sOffsetCalib);
#elif defined(AM_PART_APOLLO4P)
            am_hal_audadc_samples_read(g_AUDADCHandle,
                                       NULL,
                                       &ui32SampleCount2,
                                       true, &sLGSampleBuffer[0],
                                       true, &sHGSampleBuffer[0],
                                       NULL);
#else
            am_hal_audadc_samples_read(g_AUDADCHandle,
                                       NULL,
                                       &ui32SampleCount2,
                                       true, &sLGSampleBuffer[0],
                                       true, &sHGSampleBuffer[0]);
#endif
#else
            uint32_t ui32SampleCount2 = AUDADC_SAMPLE_BUF_SIZE;
#if defined(AM_PART_APOLLO4P) && defined(DC_OFFSET_CAL)
            am_hal_audadc_samples_read(g_AUDADCHandle,
                                       pui32_buffer,
                                       &ui32SampleCount2,
                                       true, &sLGSampleBuffer[0],
                                       true, &sHGSampleBuffer[0],
                                       &sOffsetCalib);
#elif defined(AM_PART_APOLLO4P)
            am_hal_audadc_samples_read(g_AUDADCHandle,
                                       pui32_buffer,
                                       &ui32SampleCount2,
                                       true, &sLGSampleBuffer[0],
                                       true, &sHGSampleBuffer[0],
                                       NULL);
#else
            am_hal_audadc_samples_read(g_AUDADCHandle,
                                       pui32_buffer,
                                       &ui32SampleCount2,
                                       true, &sLGSampleBuffer[0],
                                       true, &sHGSampleBuffer[0]);
#endif
#endif
            g_bAUDADCDMAComplete = false;
            if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_interrupt_clear(g_AUDADCHandle, 0xFFFFFFFF))
            {
                am_util_stdio_printf("Error - clearing the AUDADC interrupts failed.\n");
            }
            break; // single capture for selftest
        }
    }
    am_util_stdio_printf("MIC test done!\r\n");

    am_util_stdio_printf("Init LSM6D...\r\n");
    lsm6dso_init_with_int1();
    lsm6dso_xl_full_scale_set(&dev_ctx,LSM6DSO_2g);
    lsm6dso_xl_data_rate_set(&dev_ctx,LSM6DSO_XL_ODR_104Hz);
    lsm6dso_gy_full_scale_set(&dev_ctx,LSM6DSO_125dps);
    lsm6dso_gy_data_rate_set(&dev_ctx,LSM6DSO_GY_ODR_104Hz);
    lsm6dso_axis3bit16_t raw_acc,raw_gyro;
    int16_t ax,ay,az;
    float sensitivity = 0.061f;
    lsm6dso_acceleration_raw_get(&dev_ctx, raw_acc.i16bit);
    ax = raw_acc.i16bit[0];
    ay = raw_acc.i16bit[1];
    az = raw_acc.i16bit[2];

    float ax_mg=ax * sensitivity;
    float ay_mg=ay * sensitivity;
    float az_mg=az * sensitivity;

    am_util_stdio_printf("Acceleration [mg]:X=%.2f,Y=%.2f,Z=%.2f\r\n",ax_mg,ay_mg,az_mg);

    lsm6dso_angular_rate_raw_get(&dev_ctx, raw_gyro.i16bit);

    float gx_mdps=raw_gyro.i16bit[0]*4.375f;
    float gy_mdps=raw_gyro.i16bit[1]*4.375f;
    float gz_mdps=raw_gyro.i16bit[2]*4.375f;

    am_util_stdio_printf("Gyro [mdps]:X=%.2f,Y=%.2f,Z=%.2f\r\n",gx_mdps,gy_mdps,gz_mdps);

    am_util_stdio_printf("Init ALS...\r\n");
    float lux;
    sip1221_sensor_init();
    sip1221_als_enable();
    if (!sip1221_als_sample(&lux))
    {
        am_util_stdio_printf("ALS: %f\r\n",lux);
    }

    am_util_stdio_printf("Forever loop...\r\n");

    uint32_t start_tick, end_tick;
    uint32_t draw_start_tick, draw_end_tick;
    float draw_time_ms, accum_draw_time = 0;
    float time_elapsed_ms;
    float fps;
    start_tick = stimer_now();
    while (1) {
      draw_start_tick = stimer_now();
      example_draw_test_pattern(100, 100, 0xAAAA);
      draw_end_tick = stimer_now();
      draw_time_ms = stimer_elapsed_ms(draw_start_tick, draw_end_tick); 
      accum_draw_time += draw_time_ms;
      ++frame_counter;
      if (frame_counter > 100) {
        end_tick = stimer_now(); 
        time_elapsed_ms = stimer_elapsed_ms(start_tick, end_tick);
        if (time_elapsed_ms > 0) {
          fps = (float)frame_counter / (time_elapsed_ms / 1000.0f);
          am_util_stdio_printf("FPS: %.2f\n", fps);
        }
        am_util_stdio_printf("Avg draw time: %.2f\n", draw_time_ms);
        draw_time_ms = 0;
        frame_counter = 0;
        start_tick = stimer_now(); 
      }
    }

    return 0;
}
