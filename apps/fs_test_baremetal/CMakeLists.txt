# Bare-metal filesystem test firmware target

set(FS_TEST_TARGET fs_test)

set(FS_TEST_DEFINES
    PART_apollo4p
    AM_PACKAGE_BGA
    AM_PART_APOLLO4P
    AM_UTIL_FAULTISR_PRINT
    gcc
)

file(GLOB FS_TEST_SRC_ALL
    ${CMAKE_SOURCE_DIR}/src/*.c
)

# Exclude RTOS/LVGL/CLI sources not needed
list(REMOVE_ITEM FS_TEST_SRC_ALL
    ${CMAKE_SOURCE_DIR}/src/minimal_display_task.c
    ${CMAKE_SOURCE_DIR}/src/rtos.c
    ${CMAKE_SOURCE_DIR}/src/gui_task.c
    ${CMAKE_SOURCE_DIR}/src/cli_shell.c
    ${CMAKE_SOURCE_DIR}/src/my_watch.c
)

# Pull in AmbiqSuite utils needed for printf/delay/etc.
list(APPEND FS_TEST_SRC_ALL
    ${AMBIQSUITE_DIR}/utils/am_util_stdio.c
    ${AMBIQSUITE_DIR}/utils/am_util_delay.c
    ${AMBIQSUITE_DIR}/utils/am_util_id.c
    ${AMBIQSUITE_DIR}/utils/am_util_string.c
    ${AMBIQSUITE_DIR}/utils/am_util_debug.c
    ${AMBIQSUITE_DIR}/utils/am_util_faultisr.c
    ${AMBIQSUITE_DIR}/utils/am_util_syscalls.c
)

# Pull in filesystem libraries (LittleFS + Dhara)
list(APPEND FS_TEST_SRC_ALL
    ${CMAKE_SOURCE_DIR}/external/littlefs/lfs.c
    ${CMAKE_SOURCE_DIR}/external/littlefs/lfs_util.c
    ${CMAKE_SOURCE_DIR}/external/dhara/dhara/error.c
    ${CMAKE_SOURCE_DIR}/external/dhara/dhara/journal.c
    ${CMAKE_SOURCE_DIR}/external/dhara/dhara/map.c
)

# Add our bare-metal entry point
list(APPEND FS_TEST_SRC_ALL
    ${CMAKE_CURRENT_SOURCE_DIR}/main.c
)

# Ensure syscall stubs are included (explicit to avoid GLOB cache issues)
list(APPEND FS_TEST_SRC_ALL
    ${CMAKE_SOURCE_DIR}/src/syscalls_newlib.c
)

add_executable(${FS_TEST_TARGET} ${FS_TEST_SRC_ALL})

set_target_properties(${FS_TEST_TARGET} PROPERTIES
    OUTPUT_NAME ${FS_TEST_TARGET}
    SUFFIX ".axf"
)

target_include_directories(${FS_TEST_TARGET} PRIVATE
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/src/include
    ${AMBIQSUITE_DIR}/utils
)

target_compile_definitions(${FS_TEST_TARGET} PRIVATE ${FS_TEST_DEFINES})

target_link_directories(${FS_TEST_TARGET} PRIVATE
  ${AMBIQSUITE_DIR}/bsp/gcc/bin
  ${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu/gcc/bin
  ${AMBIQSUITE_DIR}/ARM/Lib/ARM
)

target_link_libraries(${FS_TEST_TARGET} PRIVATE
  ambiqsuite_includes
  am_bsp
  am_hal
  m
  ${AMBIQSUITE_DIR}/ARM/Lib/ARM/libarm_cortexM4lf_math.a
)

add_dependencies(${FS_TEST_TARGET} vendor_libs)

target_link_options(${FS_TEST_TARGET} PRIVATE
    -T${CMAKE_SOURCE_DIR}/src/linker_script.ld
    -Wl,--gc-sections
    -Wl,--entry,Reset_Handler
    -Wl,-Map,${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${FS_TEST_TARGET}.map
    -Wl,--start-group -lm -lc -lgcc -lnosys -Wl,--end-group
)

add_custom_command(TARGET ${FS_TEST_TARGET} POST_BUILD
    COMMAND ${CMAKE_OBJCOPY} -Obinary $<TARGET_FILE:${FS_TEST_TARGET}> ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${FS_TEST_TARGET}.bin
    COMMAND ${CMAKE_OBJDUMP} -S $<TARGET_FILE:${FS_TEST_TARGET}> > ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${FS_TEST_TARGET}.lst
    COMMAND ${CMAKE_SIZE} $<TARGET_FILE:${FS_TEST_TARGET}> > ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${FS_TEST_TARGET}.size
    COMMENT "Creating binary and listing files"
)

