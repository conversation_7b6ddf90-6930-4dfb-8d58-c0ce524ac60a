#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"
#include "am_util_string.h"

#include "board_init.h"
#include "fs_service.h"

int main(void) {
    board_init();
    am_util_stdio_terminal_clear();
    am_util_stdio_printf("FS test starting...\r\n");

    if (fs_service_hw_init(/*enable_ecc*/ true) < 0) {
        am_util_stdio_printf("fs_service_hw_init failed\r\n");
        while (1) {}
    }

    int r = fs_service_mount(/*format_if_needed*/ true);
    if (r != 0) {
        am_util_stdio_printf("lfs mount failed: %d\r\n", r);
        while (1) {}
    }

    // Simple create/write/read test
    lfs_t *lfs = fs_get_lfs();
    (void)fs_get_lfs_cfg();

    lfs_file_t file;
    const char *msg = "hello from littlefs over dhara!";

    r = lfs_file_open(lfs, &file, "greet.txt", LFS_O_WRONLY | LFS_O_CREAT | LFS_O_TRUNC);
    if (r < 0) {
        am_util_stdio_printf("open for write failed: %d\r\n", r);
        while (1) {}
    }
    r = lfs_file_write(lfs, &file, msg, (lfs_size_t)am_util_string_strlen(msg));
    if (r < 0) {
        am_util_stdio_printf("write failed: %d\r\n", r);
        while (1) {}
    }
    lfs_file_close(lfs, &file);

    r = lfs_file_open(lfs, &file, "greet.txt", LFS_O_RDONLY);
    if (r < 0) {
        am_util_stdio_printf("open for read failed: %d\r\n", r);
        while (1) {}
    }
    char buf[64] = {0};
    r = (int)lfs_file_read(lfs, &file, buf, sizeof(buf)-1);
    lfs_file_close(lfs, &file);
    if (r < 0) {
        am_util_stdio_printf("read failed: %d\r\n", r);
        while (1) {}
    }

    am_util_stdio_printf("Read back: %s\r\n", buf);
    am_util_stdio_printf("FS test done.\r\n");

    while (1) {
        // idle
    }
}

