#!/usr/bin/env bash
# Automatically format all C/C++ files using clang-format.

set -euo pipefail

REQUIRED_VERSION="22.0.0"

if ! command -v clang-format &> /dev/null; then
    echo "clang-format not found. Please install version $REQUIRED_VERSION."
    exit 1
fi

INSTALLED_VERSION=$(clang-format --version | grep -oE '[0-9]+\.[0-9]+(\.[0-9]+)?' | head -n1)

if [[ "$INSTALLED_VERSION" != "$REQUIRED_VERSION" ]]; then
    echo "clang-format version mismatch."
    echo "   Installed: $INSTALLED_VERSION"
    echo "   Required:  $REQUIRED_VERSION"
    exit 1
fi

# Push the current directory onto the stack and move to the root of the
# repository (parent of scripts/)
pushd "$(dirname "${BASH_SOURCE[0]}")/.." >/dev/null

# Find all .c, .h, .cc, .cpp, .hpp files and format them in place under src/
find src \
  -type d -name .git -prune -o \
  -type d -name texture -prune -o \
  -type f \( -name "*.c" -o -name "*.h" -o -name "*.cc" -o -name "*.cpp" -o -name "*.hpp" \) \
  -print0 | xargs -0 -n 1 -I{} sh -c 'echo "Formatting {}"; clang-format -i "{}"'

# Return to the original directory by popping from the stack
popd >/dev/null

echo "Done!"