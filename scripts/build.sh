#!/bin/bash

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
BUILD_TYPE="Debug"
CLEAN=false
VERBOSE=false
JOBS=$(nproc)
TARGET="hs"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -r|--release)
            BUILD_TYPE="Release"
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -j|--jobs)
            JOBS="$2"
            shift 2
            ;;
        -t|--target)
            TARGET="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -c, --clean     Clean build directory before building"
            echo "  -r, --release   Build in Release mode (default: Debug)"
            echo "  -v, --verbose   Enable verbose output"
            echo "  -j, --jobs N    Number of parallel jobs (default: $(nproc))"
            echo "  -t, --target T  Build target (hs, selftest). Default: hs"
            echo "  -h, --help      Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    print_error "CMakeLists.txt not found. Please run this script from the repository root."
    exit 1
fi

# Check if toolchain file exists
if [ ! -f "cmake/arm-gcc-toolchain.cmake" ]; then
    print_error "arm-gcc-toolchain.cmake not found. Please ensure the toolchain file is present."
    exit 1
fi

# Set build directory
BUILD_DIR="build"

print_status "Building LVGL Test Happystone Example"
print_status "Build type: $BUILD_TYPE"
print_status "Jobs: $JOBS"
print_status "Target: $TARGET"

# Clean build directory if requested
if [ "$CLEAN" = true ]; then
    print_status "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
fi

# Create build directory
mkdir -p "$BUILD_DIR"
pushd "$BUILD_DIR" >/dev/null

# Configure CMake
print_status "Configuring CMake..."
CMAKE_ARGS=(
    -DCMAKE_TOOLCHAIN_FILE=../cmake/arm-gcc-toolchain.cmake
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE"
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
)

if [ "$VERBOSE" = true ]; then
    CMAKE_ARGS+=(-DCMAKE_VERBOSE_MAKEFILE=ON)
fi

cmake "${CMAKE_ARGS[@]}" ..

# Build the project
print_status "Building project..."
BUILD_CMD=(cmake --build . --target "$TARGET" --parallel "$JOBS")

if [ "$VERBOSE" = true ]; then
    BUILD_CMD+=(--verbose)
fi

"${BUILD_CMD[@]}"

# Check if build was successful
if [ $? -eq 0 ]; then
    print_status "Build completed successfully!"
    print_status "Output files:"
    ls -lah bin/
else
    print_error "Build failed!"
    exit 1
fi

# Return to original directory
popd >/dev/null

print_status "Build script completed successfully!"
