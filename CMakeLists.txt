cmake_minimum_required(VERSION 3.16)

project(happystone VERSION 1.0.0 LANGUAGES C ASM)

# Set C standard
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Set output directory
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Set target name globally so subdirs can use it
set(TARGET_NAME hs)

# Include external dependencies
add_subdirectory(external)

# Include project modules
add_subdirectory(src)

# Additional application targets
add_subdirectory(apps)



