

Breakpoint=/home/<USER>/dev/happystone/apps/selftest_baremetal/main.c:86:115, State=BP_STATE_ON
Breakpoint=/home/<USER>/dev/happystone/apps/selftest_baremetal/main.c:105:13, State=BP_STATE_DISABLED
Breakpoint=/home/<USER>/dev/happystone/apps/selftest_baremetal/main.c:124:5, State=BP_STATE_DISABLED
Breakpoint=/home/<USER>/dev/happystone/apps/selftest_baremetal/main.c:136:5, State=BP_STATE_ON
Breakpoint=/home/<USER>/dev/happystone/apps/selftest_baremetal/main.c:144, State=BP_STATE_ON
Breakpoint=/home/<USER>/dev/happystone/apps/selftest_baremetal/main.c:154:8, State=BP_STATE_ON
Breakpoint=/home/<USER>/dev/happystone/apps/selftest_baremetal/main.c:177, State=BP_STATE_ON
Breakpoint=/home/<USER>/dev/happystone/src/flash.c:232, State=BP_STATE_DISABLED
OpenDocument="flash.c", FilePath="/home/<USER>/dev/happystone/src/flash.c", Line=210
OpenDocument="", FilePath="", Line=0
OpenDocument="main.c", FilePath="/home/<USER>/dev/happystone/apps/selftest_baremetal/main.c", Line=135
OpenToolbar="Debug", Floating=0, x=0, y=0
OpenWindow="Registers 1", DockArea=RIGHT, x=0, y=1, w=359, h=663, TabPos=1, TopOfStack=0, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0, FilteredItems=[], RefreshRate=1
OpenWindow="Disassembly", DockArea=RIGHT, x=0, y=0, w=359, h=362, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Break & Tracepoints", DockArea=LEFT, x=0, y=1, w=508, h=543, TabPos=0, TopOfStack=0, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0, VectorCatchIndexMask=0
OpenWindow="Memory 1", DockArea=BOTTOM, x=1, y=0, w=1165, h=337, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0, EditorAddress=0x1002AEA8
OpenWindow="Watched Data 1", DockArea=LEFT, x=0, y=1, w=508, h=543, TabPos=2, TopOfStack=1, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Functions", DockArea=LEFT, x=0, y=1, w=508, h=543, TabPos=1, TopOfStack=0, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Call Graph", DockArea=LEFT, x=0, y=0, w=508, h=482, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Console", DockArea=BOTTOM, x=0, y=0, w=1170, h=337, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="RTOS", DockArea=RIGHT, x=0, y=1, w=359, h=663, TabPos=0, TopOfStack=1, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0, Showing=""
SmartViewPlugin="", Page="", Toolbar="Hidden", Window="SmartView 1"
TableHeader="Registers 1", SortCol="Name", SortOrder="ASCENDING", VisibleCols=["Name";"Value";"Description"], ColWidths=[100;157;564]
TableHeader="Functions", SortCol="Name", SortOrder="ASCENDING", VisibleCols=["Name";"Address";"Size";"#Insts";"Source"], ColWidths=[27;27;27;27;100]
TableHeader="Vector Catches", SortCol="", SortOrder="ASCENDING", VisibleCols=["";"Vector Catch";"Description"], ColWidths=[50;300;538]
TableHeader="Break & Tracepoints", SortCol="", SortOrder="ASCENDING", VisibleCols=["";"Type";"Location";"Extras"], ColWidths=[100;100;109;614]
TableHeader="Call Graph", SortCol="Name", SortOrder="ASCENDING", VisibleCols=["Name";"Stack Total";"Stack Local";"Code Total";"Code Local";"Depth";"Called From"], ColWidths=[1442;130;133;127;130;135;139]
TableHeader="Power Sampling", SortCol="None", SortOrder="ASCENDING", VisibleCols=["Index";"Time";"Ch 0"], ColWidths=[100;100;100]
TableHeader="RegisterSelectionDialog", SortCol="None", SortOrder="ASCENDING", VisibleCols=[], ColWidths=[]
TableHeader="Watched Data 1", SortCol="Expression", SortOrder="ASCENDING", VisibleCols=["Expression";"Value";"Location";"Refresh"], ColWidths=[222;357;113;100]
TableHeader="TargetExceptionDialog", SortCol="Name", SortOrder="ASCENDING", VisibleCols=["Name";"Value";"Address";"Description"], ColWidths=[200;100;100;330]
WatchedExpression="buf", Window=Watched Data 1
WatchedExpression="out", Window=Watched Data 1