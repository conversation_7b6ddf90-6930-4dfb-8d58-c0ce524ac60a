// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the use
//   or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------


#ifndef ADC_H
#define ADC_H

#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"
#include "am_hal_global.h"
#include "am_hal_gpio.h"

//
// Select AUDADC clock source
// When switching clock source from XTHS to HFRC/HFRC2,
// please power cycle MCU, then run the bin with new clock source.
//
#define XTHS  0
#define HFRC  1
#define HFRC2 2
#define HFRC2_ADJ 3
#define CLK_SRC HFRC2_ADJ



#define AUDADC_EXAMPLE_DEBUG 1 // Please set this to 1 when running in debug (rtt) mode.

#define CH_A0_EN  1
#define CH_A1_EN  1
#define CH_B0_EN  0
#define CH_B1_EN  0

#define PREAMP_FULL_GAIN    6    // Enable op amps for full gain range
#define CH_A0_GAIN_DB       3
#define CH_A1_GAIN_DB       3
#define CH_B0_GAIN_DB       3
#define CH_B1_GAIN_DB       3

#define AUDADC_SAMPLE_BUF_SIZE      (2400)    // Should be padded to 12 samples follow current DMA/FIFO mechanism:
                                              // DMA trigger on FIFO 75% full
#define AUDADC_DATA_BUFFER_SIZE     (2*AUDADC_SAMPLE_BUF_SIZE)



//
// 0 - DMA buffer read, 1 - FIFO read
//
#define FIFO_READ            0
#if defined(AM_PART_APOLLO4P)
    #define DC_OFFSET_CAL
#endif

extern void *g_AUDADCHandle;

extern uint32_t g_ui32AUDADCSampleBuffer[AUDADC_SAMPLE_BUF_SIZE + AUDADC_SAMPLE_BUF_SIZE + 3];

extern int16_t g_in16AudioDataBuffer[AUDADC_DATA_BUFFER_SIZE];

extern am_hal_audadc_sample_t sLGSampleBuffer[AUDADC_SAMPLE_BUF_SIZE];
extern am_hal_audadc_sample_t sHGSampleBuffer[AUDADC_SAMPLE_BUF_SIZE];

extern volatile bool g_bAUDADCDMAComplete;

extern am_hal_audadc_dma_config_t g_sAUDADCDMAConfig;

extern am_hal_audadc_gain_config_t g_sAudadcGainConfig;

//
// AUDADC DMA error flag.
//
extern volatile bool g_bAUDADCDMAError;

void sleep(void);

void audadc_slot_config(void);

void audadc_config(void);




#endif //ADC_H