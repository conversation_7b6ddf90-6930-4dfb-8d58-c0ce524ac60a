# =============================================================================
# COMPILER DEFINITIONS
# =============================================================================

# Preprocessor definitions
set(PROJECT_DEFINES
    PART_apollo4p
    AM_FREERTOS
    AM_PACKAGE_BGA
    AM_PART_APOLLO4P
    AM_UTIL_FAULTISR_PRINT

    USE_MINIMAL_DISPLAY_TASK
    LV_AMBIQ_FB_RESX=390
    LV_AMBIQ_FB_RESY=390
    LV_AMBIQ_FB_USE_RGB565=1
    LV_AMBIQ_FB_USE_RGB888=0
    LV_CONF_INCLUDE_SIMPLE
    LV_LVGL_H_INCLUDE_SIMPLE
    NEMAGFX_SDK_PATH=${THINKSI_DIR}/NemaGFX_SDK
    NEMA_CUSTOM_MALLOC_INCLUDE="lv_ambiq_nema_hal.h"
    NEMA_PLATFORM=apollo4p_nemagfx
    NEMA_USE_CUSTOM_MALLOC
    WAIT_IRQ_BINARY_SEMAPHORE=1
    apollo4p_evb_disp_shield_rev2
    gcc
)

# Include directories handled via ambiqsuite_includes; application adds only its src include

# =============================================================================
# SOURCE FILES
# =============================================================================

# Device and utility sources (from AmbiqSuite)
set(DEVICE_SOURCES
  ${AMBIQSUITE_DIR}/devices/am_devices_display_generic.c
  ${AMBIQSUITE_DIR}/utils/am_util_delay.c
  ${AMBIQSUITE_DIR}/utils/am_util_faultisr.c
  ${AMBIQSUITE_DIR}/utils/am_util_id.c
  ${AMBIQSUITE_DIR}/utils/am_util_stdio.c
  ${AMBIQSUITE_DIR}/utils/am_util_syscalls.c
)

# Additional utility sources
set(ADDITIONAL_UTIL_SOURCES
    ../src/am_resources.c
)

# Display task sources
set(DISPLAY_TASK_SOURCES
    ${LVGL_ROOT}/ambiq_support/display_task_cpu_only.c
    ${LVGL_ROOT}/ambiq_support/display_task_fake.c
    ${LVGL_ROOT}/ambiq_support/display_task_one_and_partial_fb.c
    ${LVGL_ROOT}/ambiq_support/display_task_one_fb.c
    ${LVGL_ROOT}/ambiq_support/display_task_two_fb.c
)

# FreeRTOS sources
set(FREERTOS_SOURCES
    ${FREERTOS_ROOT}/Source/event_groups.c
    ${FREERTOS_ROOT}/Source/list.c
    ${FREERTOS_ROOT}/Source/queue.c
    ${FREERTOS_ROOT}/Source/tasks.c
    ${FREERTOS_ROOT}/Source/timers.c
    ${FREERTOS_ROOT}/Source/portable/MemMang/heap_4.c
    ${FREERTOS_ROOT}/Source/portable/GCC/AMapollo4/port.c
)

# LVGL core sources
set(LVGL_CORE_SOURCES
    ${LVGL_ROOT}/lvgl/src/core/lv_disp.c
    ${LVGL_ROOT}/lvgl/src/core/lv_event.c
    ${LVGL_ROOT}/lvgl/src/core/lv_group.c
    ${LVGL_ROOT}/lvgl/src/core/lv_indev.c
    ${LVGL_ROOT}/lvgl/src/core/lv_indev_scroll.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_class.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_draw.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_pos.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_scroll.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_style.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_style_gen.c
    ${LVGL_ROOT}/lvgl/src/core/lv_obj_tree.c
    ${LVGL_ROOT}/lvgl/src/core/lv_refr.c
    ${LVGL_ROOT}/lvgl/src/core/lv_theme.c
)

# LVGL draw sources
set(LVGL_DRAW_SOURCES
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_arc.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_blend.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_img.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_label.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_line.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_mask.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_rect.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_draw_triangle.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_img_buf.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_img_cache.c
    ${LVGL_ROOT}/lvgl/src/draw/lv_img_decoder.c
)

# LVGL font sources
set(LVGL_FONT_SOURCES
    ${LVGL_ROOT}/lvgl/src/font/lv_font.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_fmt_txt.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_loader.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_10.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_12.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_12_subpx.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_14.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_16.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_18.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_20.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_22.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_24.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_26.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_28.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_28_compressed.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_30.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_32.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_34.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_36.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_38.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_40.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_42.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_44.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_46.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_48.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_montserrat_8.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_simsun_16_cjk.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_unscii_16.c
    ${LVGL_ROOT}/lvgl/src/font/lv_font_unscii_8.c
)

# LVGL GPU sources
set(LVGL_GPU_SOURCES
    ${LVGL_ROOT}/lvgl/src/gpu/lv_gpu_ambiq_nema.c
)

# LVGL HAL sources
set(LVGL_HAL_SOURCES
    ${LVGL_ROOT}/lvgl/src/hal/lv_hal_disp.c
    ${LVGL_ROOT}/lvgl/src/hal/lv_hal_indev.c
    ${LVGL_ROOT}/lvgl/src/hal/lv_hal_tick.c
)

# LVGL layout sources
set(LVGL_LAYOUT_SOURCES
    ${LVGL_ROOT}/lvgl/src/layouts/flex/lv_flex.c
    ${LVGL_ROOT}/lvgl/src/layouts/grid/lv_grid.c
)

# LVGL library sources
set(LVGL_LIB_SOURCES
    ${LVGL_ROOT}/lvgl/src/libs/bmp/lv_bmp.c
    ${LVGL_ROOT}/lvgl/src/libs/freetype/lv_freetype.c
    ${LVGL_ROOT}/lvgl/src/libs/fsdrv/lv_fs_fatfs.c
    ${LVGL_ROOT}/lvgl/src/libs/fsdrv/lv_fs_posix.c
    ${LVGL_ROOT}/lvgl/src/libs/fsdrv/lv_fs_stdio.c
    ${LVGL_ROOT}/lvgl/src/libs/fsdrv/lv_fs_win32.c
    ${LVGL_ROOT}/lvgl/src/libs/gif/gifdec.c
    ${LVGL_ROOT}/lvgl/src/libs/gif/lv_gif.c
    ${LVGL_ROOT}/lvgl/src/libs/png/lodepng.c
    ${LVGL_ROOT}/lvgl/src/libs/png/lv_png.c
    ${LVGL_ROOT}/lvgl/src/libs/qrcode/lv_qrcode.c
    ${LVGL_ROOT}/lvgl/src/libs/qrcode/qrcodegen.c
    ${LVGL_ROOT}/lvgl/src/libs/rlottie/lv_rlottie.c
    ${LVGL_ROOT}/lvgl/src/libs/sjpg/lv_sjpg.c
    ${LVGL_ROOT}/lvgl/src/libs/sjpg/tjpgd.c
)

# LVGL misc sources
set(LVGL_MISC_SOURCES
    ${LVGL_ROOT}/lvgl/src/misc/lv_anim.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_anim_timeline.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_area.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_async.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_bidi.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_color.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_fs.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_gc.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_ll.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_log.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_math.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_mem.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_printf.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_style.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_style_gen.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_templ.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_timer.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_tlsf.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_txt.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_txt_ap.c
    ${LVGL_ROOT}/lvgl/src/misc/lv_utils.c
)

# LVGL other sources
set(LVGL_OTHER_SOURCES
    ${LVGL_ROOT}/lvgl/src/others/snapshot/lv_snapshot.c
)

# LVGL theme sources
set(LVGL_THEME_SOURCES
    ${LVGL_ROOT}/lvgl/src/themes/basic/lv_theme_basic.c
    ${LVGL_ROOT}/lvgl/src/themes/default/lv_theme_default.c
    ${LVGL_ROOT}/lvgl/src/themes/mono/lv_theme_mono.c
)

# LVGL widget sources (selected widgets)
set(LVGL_WIDGET_SOURCES
    ${LVGL_ROOT}/lvgl/src/widgets/animimg/lv_animimg.c
    ${LVGL_ROOT}/lvgl/src/widgets/arc/lv_arc.c
    ${LVGL_ROOT}/lvgl/src/widgets/bar/lv_bar.c
    ${LVGL_ROOT}/lvgl/src/widgets/btn/lv_btn.c
    ${LVGL_ROOT}/lvgl/src/widgets/btnmatrix/lv_btnmatrix.c
    ${LVGL_ROOT}/lvgl/src/widgets/calendar/lv_calendar.c
    ${LVGL_ROOT}/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c
    ${LVGL_ROOT}/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c
    ${LVGL_ROOT}/lvgl/src/widgets/canvas/lv_canvas.c
    ${LVGL_ROOT}/lvgl/src/widgets/chart/lv_chart.c
    ${LVGL_ROOT}/lvgl/src/widgets/checkbox/lv_checkbox.c
    ${LVGL_ROOT}/lvgl/src/widgets/colorwheel/lv_colorwheel.c
    ${LVGL_ROOT}/lvgl/src/widgets/dropdown/lv_dropdown.c
    ${LVGL_ROOT}/lvgl/src/widgets/img/lv_img.c
    ${LVGL_ROOT}/lvgl/src/widgets/imgbtn/lv_imgbtn.c
    ${LVGL_ROOT}/lvgl/src/widgets/keyboard/lv_keyboard.c
    ${LVGL_ROOT}/lvgl/src/widgets/label/lv_label.c
    ${LVGL_ROOT}/lvgl/src/widgets/led/lv_led.c
    ${LVGL_ROOT}/lvgl/src/widgets/line/lv_line.c
    ${LVGL_ROOT}/lvgl/src/widgets/list/lv_list.c
    ${LVGL_ROOT}/lvgl/src/widgets/meter/lv_meter.c
    ${LVGL_ROOT}/lvgl/src/widgets/msgbox/lv_msgbox.c
    ${LVGL_ROOT}/lvgl/src/widgets/objx_templ/lv_objx_templ.c
    ${LVGL_ROOT}/lvgl/src/widgets/roller/lv_roller.c
    ${LVGL_ROOT}/lvgl/src/widgets/slider/lv_slider.c
    ${LVGL_ROOT}/lvgl/src/widgets/span/lv_span.c
    ${LVGL_ROOT}/lvgl/src/widgets/spinbox/lv_spinbox.c
    ${LVGL_ROOT}/lvgl/src/widgets/spinner/lv_spinner.c
    ${LVGL_ROOT}/lvgl/src/widgets/switch/lv_switch.c
    ${LVGL_ROOT}/lvgl/src/widgets/table/lv_table.c
    ${LVGL_ROOT}/lvgl/src/widgets/tabview/lv_tabview.c
    ${LVGL_ROOT}/lvgl/src/widgets/textarea/lv_textarea.c
    ${LVGL_ROOT}/lvgl/src/widgets/tileview/lv_tileview.c
    ${LVGL_ROOT}/lvgl/src/widgets/win/lv_win.c
)

# LVGL Ambiq support sources
set(LVGL_AMBIQ_SOURCES
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_decoder.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_font_align.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_fs.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_misc.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_nema_hal.c
    ${LVGL_ROOT}/ambiq_support/lv_ambiq_touch.c
)

# ThinkSi NemaGFX sources
set(THINKSI_SOURCES
  ${THINKSI_DIR}/config/apollo4p_nemagfx/nema_dc_hal.c
  ${THINKSI_DIR}/config/apollo4p_nemagfx/nema_event.c
  ${THINKSI_DIR}/config/apollo4p_nemagfx/nema_hal.c
  ${THINKSI_DIR}/config/apollo4p_nemagfx/nema_utils.c
)

# Dhara sources (FTL core)
set(DHARA_SOURCES
  ${CMAKE_SOURCE_DIR}/external/dhara/dhara/error.c
  ${CMAKE_SOURCE_DIR}/external/dhara/dhara/journal.c
  ${CMAKE_SOURCE_DIR}/external/dhara/dhara/map.c
)
# LittleFS sources
set(LFS_SOURCES
  ${CMAKE_SOURCE_DIR}/external/littlefs/lfs.c
  ${CMAKE_SOURCE_DIR}/external/littlefs/lfs_util.c
)
# FreeRTOS-Plus-CLI sources
set(FREERTOS_CLI_SOURCES
  ${CMAKE_SOURCE_DIR}/external/FreeRTOS-Plus-CLI_202411.00/FreeRTOS_CLI.c
)



# Project-specific sources
set(PROJECT_SOURCES
  ${CMAKE_CURRENT_SOURCE_DIR}/board_init.c
  ${CMAKE_CURRENT_SOURCE_DIR}/amoled.c
  ${CMAKE_CURRENT_SOURCE_DIR}/aps12808l.c
  ${CMAKE_CURRENT_SOURCE_DIR}/gui_task.c
  ${CMAKE_CURRENT_SOURCE_DIR}/hynitron_core.c
  ${CMAKE_CURRENT_SOURCE_DIR}/hynitron_cst92xx.c
  ${CMAKE_CURRENT_SOURCE_DIR}/i2c.c
  ${CMAKE_CURRENT_SOURCE_DIR}/sh8601_apollo4.c
  ${CMAKE_CURRENT_SOURCE_DIR}/sh8601_example.c
  ${CMAKE_CURRENT_SOURCE_DIR}/uart.c
  ${CMAKE_CURRENT_SOURCE_DIR}/minimal_display_task.c
  ${CMAKE_CURRENT_SOURCE_DIR}/minimal_mem.c

  ${CMAKE_CURRENT_SOURCE_DIR}/rtos.c
  ${CMAKE_CURRENT_SOURCE_DIR}/am_resources.c
  ${CMAKE_CURRENT_SOURCE_DIR}/syscalls_newlib.c

  # Use the user's MSPI2 SPI-mode bring-up and handle definition
  ${CMAKE_CURRENT_SOURCE_DIR}/flash.c

  ${CMAKE_CURRENT_SOURCE_DIR}/nand_spi.c
  ${CMAKE_CURRENT_SOURCE_DIR}/dhara_port.c
  ${CMAKE_CURRENT_SOURCE_DIR}/lfs_dhara_bd.c
  ${CMAKE_CURRENT_SOURCE_DIR}/fs_service.c
  ${CMAKE_CURRENT_SOURCE_DIR}/cli_shell.c
)

# Combine all sources (link LVGL, FreeRTOS, and ambiq_support via external targets)
set(ALL_SOURCES
  ${DEVICE_SOURCES}
  ${THINKSI_SOURCES}
  ${DHARA_SOURCES}
  ${LFS_SOURCES}
  ${FREERTOS_CLI_SOURCES}
  ${PROJECT_SOURCES}
)

# =============================================================================
# DEPENDENCIES
# =============================================================================

# Vendor libraries are provided by external/ targets (linked below)

# =============================================================================

# CREATE EXECUTABLE
# =============================================================================

# Create the core library (shared across apps)
add_library(hs_core STATIC ${ALL_SOURCES})
# LittleFS headers
target_include_directories(hs_core PRIVATE ${CMAKE_SOURCE_DIR}/external/littlefs)
# FreeRTOS-Plus-CLI headers
target_include_directories(hs_core PRIVATE ${CMAKE_SOURCE_DIR}/external/FreeRTOS-Plus-CLI_202411.00)

# Include directories for the core library
target_include_directories(hs_core PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
# App-specific include needed for hs_core (lvgl_test.h moved under apps)
target_include_directories(hs_core PRIVATE ${CMAKE_SOURCE_DIR}/apps/lvgl_test)

# Dhara headers
target_include_directories(hs_core PRIVATE ${CMAKE_SOURCE_DIR}/external/dhara/dhara)

# Link interface include dirs for AmbiqSuite (headers like am_bsp.h)
target_link_libraries(hs_core PUBLIC ambiqsuite_includes)


# Compile definitions for the core library
target_compile_definitions(hs_core PRIVATE ${PROJECT_DEFINES})

# Note: Executable targets, link options, and post-build steps are now defined under apps/

# Ensure vendor libs are built first for the core as well
add_dependencies(hs_core vendor_libs)

# =============================================================================
# BUILD DEPENDENCIES
# =============================================================================


# =============================================================================
# INSTALL TARGETS
# =============================================================================
