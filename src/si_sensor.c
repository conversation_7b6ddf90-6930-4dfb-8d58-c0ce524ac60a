#include "si_sensor.h"
#include <string.h>
#include "i2c.h"

#define I2C_BUS 2
static uint8_t sip_i2c_addr;

//replace customer i2c write function
int8_t sip_write_regs(uint8_t reg, uint8_t *values, uint8_t count)
{
    uint8_t data[255];
    data[0] = reg;
    memcpy(&data[1],values,count);
		return i2c_write_bytes(&g_pIOMHandle_7, 0x58, reg, &data[1], count);
    //return  i2c_multi_write(I2C_BUS,sip_i2c_addr << 1,data,count +1);
}

int8_t sip_write_reg(uint8_t reg, uint8_t values)
{
    return sip_write_regs(reg, &values, 1);
}

//replace customer i2c read function
int8_t sip_read_regs(uint8_t reg, uint8_t *data, uint8_t r_size)
{
  return i2c_read_bytes(&g_pIOMHandle_7, 0x58, reg, data, r_size);  
	//return i2c_multi_read(I2C_BUS,sip_i2c_addr << 1,reg,data,r_size);
}

int8_t sip_read_reg(uint8_t reg, uint8_t *data)
{
    return sip_read_regs(reg, data, 1);
}

int8_t sip_modify_reg(uint8_t reg, uint8_t value, uint8_t mask)
{
    int ret = -1;
    uint8_t rw_buffer = 0;

    if (0xFF == mask)
    {
        ret = sip_write_reg(reg, value);
    }
    else
    {
        /* Read current value from this register */
        ret = sip_read_reg(reg, &rw_buffer);
        if (!ret)
        {
            rw_buffer &= ~mask;
            rw_buffer |= (value & mask);
            ret = sip_write_reg(reg, rw_buffer);
        }
    }

    return ret;
}

void sip_attach_address(uint8_t reg)
{
    sip_i2c_addr = reg;
}