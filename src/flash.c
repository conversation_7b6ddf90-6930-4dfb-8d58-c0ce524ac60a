// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the use
//   or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------

#include <stdint.h>
#include <stddef.h>
#include <string.h>

#include "flash.h"
#include "storage_config.h"

int flash_set_addr_len_bytes(uint8_t nbytes)
{
    am_hal_mspi_instr_addr_t cfg;
    cfg.eInstrCfg = AM_HAL_MSPI_INSTR_1_BYTE;
    switch (nbytes)
    {
        case 1: cfg.eAddrCfg = AM_HAL_MSPI_ADDR_1_BYTE; break;
        case 2: cfg.eAddrCfg = AM_HAL_MSPI_ADDR_2_BYTE; break;
        case 3: cfg.eAddrCfg = AM_HAL_MSPI_ADDR_3_BYTE; break;
        case 4: cfg.eAddrCfg = AM_HAL_MSPI_ADDR_4_BYTE; break;
        default: return -1;
    }
    uint32_t rc = am_hal_mspi_control(g_Mspi2Handle, AM_HAL_MSPI_REQ_SET_INSTR_ADDR_LEN, &cfg);
    return (rc == AM_HAL_STATUS_SUCCESS) ? 0 : -1;
}

int flash_get_feature(uint8_t addr, uint8_t *val)
{
    if (!val) return -1;
    if (flash_set_addr_len_bytes(1) < 0) return -1;
    am_hal_mspi_pio_transfer_t xfer = {0};
    xfer.ui32NumBytes = 1;
    xfer.eDirection = AM_HAL_MSPI_RX;
    xfer.bSendInstr = true;
    xfer.ui16DeviceInstr = NAND_CMD_GET_FEATURES;
    xfer.bSendAddr = true;
    xfer.ui32DeviceAddr = addr;
    xfer.bTurnaround = true;
    xfer.pui32Buffer = (uint32_t*)(uintptr_t)val;
    uint32_t rc = am_hal_mspi_blocking_transfer(g_Mspi2Handle, &xfer, 10000);
    if (flash_set_addr_len_bytes(3) < 0) return -1;
    return (rc == AM_HAL_STATUS_SUCCESS) ? 0 : -1;
}

int flash_set_feature(uint8_t addr, uint8_t val)
{
    if (flash_set_addr_len_bytes(1) < 0) return -1;
    am_hal_mspi_pio_transfer_t xfer = {0};
    xfer.ui32NumBytes = 1;
    xfer.eDirection = AM_HAL_MSPI_TX;
    xfer.bSendInstr = true;
    xfer.ui16DeviceInstr = NAND_CMD_SET_FEATURES;
    xfer.bSendAddr = true;
    xfer.ui32DeviceAddr = addr;
    uint32_t temp = val;
    xfer.pui32Buffer = &temp;
    uint32_t rc = am_hal_mspi_blocking_transfer(g_Mspi2Handle, &xfer, 10000);
    if (flash_set_addr_len_bytes(3) < 0) return -1;
    return (rc == AM_HAL_STATUS_SUCCESS) ? 0 : -1;
}

int flash_wait_ready_us(uint32_t timeout_us, uint8_t *status_out)
{
    const uint32_t ticks_per_us = 6;
    uint32_t t_start = am_hal_stimer_counter_get();
    while (1)
    {
        uint8_t status = 0;
        if (flash_get_feature(NAND_FEATURE_ADDR_STATUS, &status) < 0) return -1;
        if ((status & NAND_STATUS_OIP_MASK) == 0)
        {
            if (status_out) *status_out = status;
            return 0;
        }
        am_util_delay_us(10);
        if (timeout_us)
        {
            uint32_t t_now = am_hal_stimer_counter_get();
            uint32_t elapsed = (t_now >= t_start) ? (t_now - t_start) : (0xFFFFFFFFu - t_start + t_now + 1u);
            if (elapsed > timeout_us * ticks_per_us) return -1;
        }
    }
}



void *g_Mspi2Handle = NULL;

// MSPI2 初始化，SPI模式
uint32_t mspi2_init_spi_mode(void)
{
    uint32_t status;
	
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI2_CE0, g_AM_BSP_GPIO_MSPI2_CE0);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI2_SCK, g_AM_BSP_GPIO_MSPI2_SCK);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI2_D0, g_AM_BSP_GPIO_MSPI2_D0);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI2_D1, g_AM_BSP_GPIO_MSPI2_D1);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI2_D2, g_AM_BSP_GPIO_MSPI2_D2);
    am_hal_gpio_pinconfig(AM_BSP_GPIO_MSPI2_D3, g_AM_BSP_GPIO_MSPI2_D3);

	// qingsi: 不需要这个更底层的power ctrl，am_hal_mspi_power_control会负责调用的
    // 使能MSPI2的硬件电源 (注意：视BSP配置可能已在BSP中开启)
    //am_hal_pwrctrl_periph_enable(AM_HAL_PWRCTRL_PERIPH_MSPI2);

    // 初始化MSPI2控制器
    status = am_hal_mspi_initialize(2, &g_Mspi2Handle);
    if (status != AM_HAL_STATUS_SUCCESS) return status;

    status = am_hal_mspi_power_control(g_Mspi2Handle, AM_HAL_SYSCTRL_WAKE, false);
    if (status != AM_HAL_STATUS_SUCCESS) return status;

    // MSPI控制器配置 (基本配置，不需要TCB)
    am_hal_mspi_config_t mspiCfg = {
        .ui32TCBSize = 0,
        .pTCB = NULL,
        .bClkonD4 = false
    };
    status = am_hal_mspi_configure(g_Mspi2Handle, &mspiCfg);
    if (status != AM_HAL_STATUS_SUCCESS) return status;

    // 设备配置 (SPI标准模式)
    // leo: Mode 0 also works per datasheet, but requires 1 turnaround before
    // read in a transaction. This is because "Input data is latched on the
    // rising edge of SCLK and data shifts out on the falling edge of SCLK for
    // both modes." Mode 3 naturally has a half-cycle turnaround with respect to
    // this device design. We use mode 3 for simplicity.
    am_hal_mspi_dev_config_t devCfg = {
        .eDeviceConfig = AM_HAL_MSPI_FLASH_SERIAL_CE0,
        .eClockFreq = AM_HAL_MSPI_CLK_48MHZ,
        .eSpiMode = AM_HAL_MSPI_SPI_MODE_3,
        .ui8TurnAround = 0,
        .eInstrCfg = AM_HAL_MSPI_INSTR_1_BYTE,
        .eAddrCfg = AM_HAL_MSPI_ADDR_3_BYTE,
        .ui16ReadInstr = 0,
        .ui16WriteInstr = 0,
        .bSendAddr = false,
        .bSendInstr = false,
        .bTurnaround = false,
        .bEnWriteLatency = false,
        .ui8WriteLatency = 0,
        .bEmulateDDR = false,
        .ui16DMATimeLimit = 0,
        .eDMABoundary = AM_HAL_MSPI_BOUNDARY_NONE
    };
    status = am_hal_mspi_device_configure(g_Mspi2Handle, &devCfg);
    if (status != AM_HAL_STATUS_SUCCESS) return status;

    // 使能 MSPI 控制器
    status = am_hal_mspi_enable(g_Mspi2Handle);
    if (status != AM_HAL_STATUS_SUCCESS) return status;

    am_util_stdio_printf("MSPI2 SPI模式初始化完成\r\n");
    return AM_HAL_STATUS_SUCCESS;
}

// SPI 写函数（阻塞式 PIO 模式）
uint32_t mspi2_write(uint8_t instr, uint32_t addr, uint8_t *data, uint32_t length)
{
    am_hal_mspi_pio_transfer_t pTransaction = {0};

    pTransaction.ui32NumBytes = length;
    pTransaction.bScrambling = false;
    pTransaction.eDirection = AM_HAL_MSPI_TX;
    pTransaction.bSendInstr = true;
    pTransaction.ui16DeviceInstr = instr;
    pTransaction.bSendAddr = true;
    pTransaction.ui32DeviceAddr = addr;
    pTransaction.pui32Buffer = (uint32_t*)data;

    return am_hal_mspi_blocking_transfer(g_Mspi2Handle, &pTransaction, 10000);
}

// SPI 读函数（阻塞式 PIO 模式）
uint32_t mspi2_read(uint8_t instr, uint32_t addr, uint8_t *data, uint32_t length)
{
    am_hal_mspi_pio_transfer_t pTransaction = {0};

    pTransaction.ui32NumBytes = length;
    pTransaction.bScrambling = false;
    pTransaction.eDirection = AM_HAL_MSPI_RX;
    pTransaction.bSendInstr = true;
    pTransaction.ui16DeviceInstr = instr;
    pTransaction.bSendAddr = true;
    pTransaction.ui32DeviceAddr = addr;
    pTransaction.bTurnaround = true;
    pTransaction.pui32Buffer = (uint32_t*)data;

    return am_hal_mspi_blocking_transfer(g_Mspi2Handle, &pTransaction, 10000);
}

int flash_test_program_page(uint32_t page_addr_row, uint16_t column, const uint8_t *buf, size_t len)
{
    if (!g_Mspi2Handle || !buf || len == 0 || len > (NAND_PAGE_SIZE + NAND_OOB_SIZE) || (uint32_t)column + len > (NAND_PAGE_SIZE + NAND_OOB_SIZE)) return -1;

    if (flash_set_addr_len_bytes(2) < 0) return -1;
    // am_hal_mspi_pio_transfer_t wren = {0};
    // wren.eDirection = AM_HAL_MSPI_TX;
    // wren.bSendInstr = true;
    // wren.bSendAddr = false;
    // wren.ui16DeviceInstr = NAND_CMD_WRITE_ENABLE;
    // if (am_hal_mspi_blocking_transfer(g_Mspi2Handle, &wren, 10000) != AM_HAL_STATUS_SUCCESS) return -1;

    am_hal_mspi_pio_transfer_t load = {0};
    load.ui32NumBytes = (uint32_t)len;
    load.eDirection = AM_HAL_MSPI_TX;
    load.bSendInstr = true;
    load.ui16DeviceInstr = NAND_CMD_PROG_LOAD;
    load.bSendAddr = true;
    load.ui32DeviceAddr = column & 0xFFFFu;
    load.pui32Buffer = (uint32_t*)(uintptr_t)buf;
    uint32_t rc = am_hal_mspi_blocking_transfer(g_Mspi2Handle, &load, 10000);
    if (rc != AM_HAL_STATUS_SUCCESS) return -1;

    if (flash_set_addr_len_bytes(3) < 0) return -1;

    am_hal_mspi_pio_transfer_t wren_exec = {0};
    wren_exec.eDirection = AM_HAL_MSPI_TX;
    wren_exec.bSendInstr = true;
    wren_exec.bSendAddr = false;
    wren_exec.ui16DeviceInstr = NAND_CMD_WRITE_ENABLE;
    if (am_hal_mspi_blocking_transfer(g_Mspi2Handle, &wren_exec, 10000) != AM_HAL_STATUS_SUCCESS) return -1;

    am_hal_mspi_pio_transfer_t exec = {0};
    exec.eDirection = AM_HAL_MSPI_TX;
    exec.bSendInstr = true;
    exec.ui16DeviceInstr = NAND_CMD_PROG_EXEC;
    exec.bSendAddr = true;
    exec.ui32DeviceAddr = page_addr_row & 0x00FFFFFFu;
    if (am_hal_mspi_blocking_transfer(g_Mspi2Handle, &exec, 10000) != AM_HAL_STATUS_SUCCESS) return -1;

    uint8_t status = 0;
    if (flash_wait_ready_us(2000, &status) < 0) return -1;
    if (status & NAND_STATUS_P_FAIL_MASK) return -2;
    return 0;
}

int flash_read_page(uint32_t page_addr_row, uint16_t column, uint8_t *out, size_t len)
{
    if (!g_Mspi2Handle || !out || len == 0 || len > (NAND_PAGE_SIZE + NAND_OOB_SIZE)) return -1;

    if (flash_set_addr_len_bytes(3) < 0) return -1;

    am_hal_mspi_pio_transfer_t to_cache = {0};
    to_cache.eDirection = AM_HAL_MSPI_TX;
    to_cache.bSendInstr = true;
    to_cache.ui16DeviceInstr = NAND_CMD_PAGE_READ_TO_CACHE;
    to_cache.bSendAddr = true;
    to_cache.ui32DeviceAddr = page_addr_row & 0x00FFFFFFu;
    if (am_hal_mspi_blocking_transfer(g_Mspi2Handle, &to_cache, 10000) != AM_HAL_STATUS_SUCCESS) return -1;

    if (flash_wait_ready_us(2000, NULL) < 0) return -1;

    if (flash_set_addr_len_bytes(2) < 0) return -1;
    uint8_t staging[NAND_PAGE_SIZE + NAND_OOB_SIZE + 1];
    if (mspi2_read(NAND_CMD_READ_FROM_CACHE, column, staging, (uint32_t)(len + 1)) != AM_HAL_STATUS_SUCCESS) return -1;
    memcpy(out, staging + 1, len);

    flash_set_addr_len_bytes(3);

    return 0;
}

uint8_t gpio_flash_test(void)
{
		am_hal_gpio_pincfg_t gpio_output_config =
    {
        .GP.cfg_b.uFuncSel            = 3,    //
        .GP.cfg_b.eDriveStrength      = AM_HAL_GPIO_PIN_DRIVESTRENGTH_0P1X,
        .GP.cfg_b.ePullup             = AM_HAL_GPIO_PIN_PULLUP_NONE,
        .GP.cfg_b.ePowerSw            = AM_HAL_GPIO_PIN_POWERSW_NONE,
        .GP.cfg_b.eGPInput            = AM_HAL_GPIO_PIN_INPUT_NONE, // ???
        .GP.cfg_b.eGPOutCfg          	= AM_HAL_GPIO_PIN_OUTCFG_PUSHPULL, // ????
        .GP.cfg_b.eIntDir             = AM_HAL_GPIO_PIN_INTDIR_NONE, // ?????
    };
		
    am_hal_gpio_pincfg_t gpio_input_config =
    {
        .GP.cfg_b.uFuncSel            = 3,    //
        .GP.cfg_b.eDriveStrength      = AM_HAL_GPIO_PIN_DRIVESTRENGTH_0P1X,
        .GP.cfg_b.ePullup             = AM_HAL_GPIO_PIN_PULLUP_100K,
        .GP.cfg_b.ePowerSw            = AM_HAL_GPIO_PIN_POWERSW_NONE,
        .GP.cfg_b.eGPInput            = AM_HAL_GPIO_PIN_INPUT_ENABLE, // ???
        .GP.cfg_b.eGPOutCfg          	= AM_HAL_GPIO_PIN_OUTCFG_DISABLE, // ????
        .GP.cfg_b.eIntDir             = AM_HAL_GPIO_PIN_INTDIR_NONE, // ?????
    };
		
		uint8_t i=0;
		uint32_t ui32ReadValue = 0;
		
		am_hal_gpio_pinconfig(82, gpio_output_config);//IO pin for CLK
		am_hal_gpio_pinconfig(57, gpio_output_config);//IO pin for CS_N
		am_hal_gpio_pinconfig(74, gpio_output_config);//IO pin for MOSI
		am_hal_gpio_pinconfig(75, gpio_input_config);//IO pin for MISO
		
		am_hal_gpio_state_write(82, AM_HAL_GPIO_OUTPUT_SET);
		am_hal_gpio_state_write(57, AM_HAL_GPIO_OUTPUT_SET);
		am_hal_gpio_state_write(74, AM_HAL_GPIO_OUTPUT_SET);
		
		
		am_hal_gpio_state_write(57, AM_HAL_GPIO_OUTPUT_CLEAR);//CS_N=0
		uint8_t write_data=0x9f;
		uint8_t read_data=0;
		uint8_t read_data1=0;
		for(i=0;i<8;i++)
		{
			am_hal_gpio_state_write(82, AM_HAL_GPIO_OUTPUT_CLEAR);//clk=0
			if (write_data & 0x80)
			{
				am_hal_gpio_state_write(74, AM_HAL_GPIO_OUTPUT_SET);
			}
			else
			{
				am_hal_gpio_state_write(74, AM_HAL_GPIO_OUTPUT_CLEAR);
			}
			write_data=write_data<<1;
			//am_util_delay_us(100);
			am_hal_gpio_state_write(82, AM_HAL_GPIO_OUTPUT_SET);//clk=1
			//am_util_delay_us(100);
		}
		
		write_data=0x9f;
		for(i=0;i<8;i++)
		{
			am_hal_gpio_state_write(82, AM_HAL_GPIO_OUTPUT_CLEAR);//clk=0
			if (write_data & 0x80)
			{
				am_hal_gpio_state_write(74, AM_HAL_GPIO_OUTPUT_SET);
			}
			else
			{
				am_hal_gpio_state_write(74, AM_HAL_GPIO_OUTPUT_CLEAR);
			}
			write_data=write_data<<1;
			//am_util_delay_us(100);
			am_hal_gpio_state_write(82, AM_HAL_GPIO_OUTPUT_SET);//clk=1
			//am_util_delay_us(100);
		}
		
		write_data=0x9f;
		for(i=0;i<8;i++)
		{
			read_data=read_data<<1;
			am_hal_gpio_state_write(82, AM_HAL_GPIO_OUTPUT_CLEAR);//clk=0
			if (write_data & 0x80)
			{
				am_hal_gpio_state_write(74, AM_HAL_GPIO_OUTPUT_SET);
			}
			else
			{
				am_hal_gpio_state_write(74, AM_HAL_GPIO_OUTPUT_CLEAR);
			}
			write_data=write_data<<1;
			//am_util_delay_us(100);
			am_hal_gpio_state_write(82, AM_HAL_GPIO_OUTPUT_SET);//clk=1
			am_hal_gpio_state_read(75,AM_HAL_GPIO_INPUT_READ, &ui32ReadValue);
			read_data|=ui32ReadValue;
			//am_util_delay_us(100);
		}
		
		write_data=0x9f;
		for(i=0;i<8;i++)
		{
			read_data1=read_data1<<1;
			am_hal_gpio_state_write(82, AM_HAL_GPIO_OUTPUT_CLEAR);//clk=0
			if (write_data & 0x80)
			{
				am_hal_gpio_state_write(74, AM_HAL_GPIO_OUTPUT_SET);
			}
			else
			{
				am_hal_gpio_state_write(74, AM_HAL_GPIO_OUTPUT_CLEAR);
			}
			write_data=write_data<<1;
			//am_util_delay_us(100);
			am_hal_gpio_state_write(82, AM_HAL_GPIO_OUTPUT_SET);//clk=1
			am_hal_gpio_state_read(75,AM_HAL_GPIO_INPUT_READ, &ui32ReadValue);
			read_data1|=ui32ReadValue;
			//am_util_delay_us(100);
		}
		
		am_hal_gpio_state_write(57, AM_HAL_GPIO_OUTPUT_SET);//CS_N=1
		
		am_util_stdio_printf("FLASH Manufacturer ID 值是%x\r\n",read_data);
		am_util_stdio_printf("FLASH Device ID 值是%x\r\n",read_data1);
		return 0;

}
