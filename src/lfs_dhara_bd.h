#ifndef LFS_DHARA_BD_H
#define LFS_DHARA_BD_H

#include <stdint.h>
#include <stdbool.h>
#include "storage_config.h"
#include "dhara_port.h"
#include "../external/dhara/dhara/map.h"
#include "../external/littlefs/lfs.h"

#ifdef __cplusplus
extern "C" {
#endif

// Populate an lfs_config that uses Dhara map as the backing block device.
// - map must remain valid for the lifetime of the filesystem
// - sizes are derived from storage_config.h; tune via those macros
void lfs_dhara_default_config(struct lfs_config *cfg, struct dhara_map *map);

#ifdef __cplusplus
}
#endif

#endif // LFS_DHARA_BD_H

