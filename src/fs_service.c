#include <string.h>
#include "fs_service.h"
#include "storage_config.h"
#include "nand_spi.h"
#include "dhara_port.h"
#include "lfs_dhara_bd.h"
#ifdef AM_FREERTOS
#include "FreeRTOS.h"
#include "semphr.h"
#endif

static struct dhara_map g_map;
static uint8_t g_pagebuf[NAND_PAGE_SIZE];
static lfs_t g_lfs;
static struct lfs_config g_lfs_cfg;
#ifdef AM_FREERTOS
static SemaphoreHandle_t g_fs_mutex;
#endif

static inline void fs_lock(void) {
#ifdef AM_FREERTOS
    if (g_fs_mutex) xSemaphoreTake(g_fs_mutex, portMAX_DELAY);
#else
    (void)0;
#endif
}
static inline void fs_unlock(void) {
#ifdef AM_FREERTOS
    if (g_fs_mutex) xSemaphoreGive(g_fs_mutex);
#else
    (void)0;
#endif
}

int fs_service_hw_init(bool enable_ecc) {
    // Init MSPI/NAND
    if (nand_init() < 0) return -1;

    // Configure on-die ECC as requested
    if (nand_set_ecc_enabled(enable_ecc) < 0) return -1;

    // Init Dhara geometry descriptor
    if (dhara_port_init() < 0) return -1;

    // Initialize Dhara map with GC ratio (tunable)
    memset(&g_map, 0, sizeof(g_map));
    dhara_map_init(&g_map, &g_dhara_nand, g_pagebuf, /*gc_ratio*/ 8);

    // Try to resume existing state; it's ok if resume fails (fresh device)
    dhara_error_t err = 0;
    (void)dhara_map_resume(&g_map, &err);

    // Build LFS config targeting Dhara map
    lfs_dhara_default_config(&g_lfs_cfg, &g_map);

    // Create mutex if running under FreeRTOS; otherwise, no-op
#ifdef AM_FREERTOS
    g_fs_mutex = xSemaphoreCreateMutex();
    return (g_fs_mutex != NULL) ? 0 : -1;
#else
    return 0;
#endif
}

int fs_service_mount(bool format_if_needed) {
    fs_lock();
    int r = lfs_mount(&g_lfs, &g_lfs_cfg);
    if (r == 0) {
        fs_unlock();
        return 0;
    }
    if (!format_if_needed) {
        fs_unlock();
        return r;
    }
    r = lfs_format(&g_lfs, &g_lfs_cfg);
    if (r == 0) r = lfs_mount(&g_lfs, &g_lfs_cfg);
    fs_unlock();
    return r;
}

int fs_service_unmount(void) {
    fs_lock();
    int r = lfs_unmount(&g_lfs);
    fs_unlock();
    return r;
}

int fs_service_format(void) {
    fs_lock();
    int r = lfs_format(&g_lfs, &g_lfs_cfg);
    fs_unlock();
    return r;
}

int fs_service_fsinfo(struct lfs_fsinfo *info, lfs_ssize_t *used_blocks) {
    if (!info) return -1;
    fs_lock();
    int r = lfs_fs_stat(&g_lfs, info);
    if (r == 0 && used_blocks) {
        *used_blocks = lfs_fs_size(&g_lfs);
    }
    fs_unlock();
    return r;
}

void fs_acquire(void) { fs_lock(); }
void fs_release(void) { fs_unlock(); }

const struct lfs_config *fs_get_lfs_cfg(void) { return &g_lfs_cfg; }
lfs_t *fs_get_lfs(void) { return &g_lfs; }
struct dhara_map *fs_get_dhara_map(void) { return &g_map; }

