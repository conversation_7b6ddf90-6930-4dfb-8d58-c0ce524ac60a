// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the use
//   or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------



#include "motor.h"


#define DRV2605_ADDR  0x5A  // DRV2605 默认7位I2C地址

// I2C 写寄存器封装
void drv2605_write_reg(uint8_t reg, uint8_t data)
{
    i2c_write_byte(&g_pIOMHandle_3,DRV2605_ADDR, reg, data);
}

// I2C 读寄存器封装
uint8_t drv2605_read_reg(uint8_t reg)
{
    return i2c_read_byte(&g_pIOMHandle_3,DRV2605_ADDR, reg);
}

/// DRV2605 初始化函数（针对LRA电机）
void drv2605_init_lra(void)
{
    // 软件复位（可选）
    drv2605_write_reg(0x01, 0x80);
		am_util_delay_ms(10);

    // 设置为内部库模式（默认）
    drv2605_write_reg(0x01, 0x00);
		am_util_delay_ms(10);

    // 设置Rated Voltage（单位 5.6mV/LSB）
    //uint8_t rated_voltage = (uint16_t)(1697.0 / 5.6); // 1.2VRMS → 1697mV peak
    drv2605_write_reg(0x16, 0x2f);
		am_util_delay_ms(100);// if we don't put the delay here, the readback won't be correct, and then we stuck here forever...
		if(drv2605_read_reg(0x16)==0x2f)
		{
			am_util_stdio_printf("DRV2605 reg 0x16 readback is correct!\r\n");
		}
		else
		{
			am_util_stdio_printf("DRV2605 reg 0x16 readback is wrong!\r\n");
		}

    // 设置Overdrive Clamp Voltage（一般Rated * 1.3）
    //uint8_t overdrive_voltage = (uint16_t)(2200.0 / 5.6);
    drv2605_write_reg(0x17, 0x5e);
		am_util_delay_ms(100);// if we don't put the delay here, the readback won't be correct, and then we stuck here forever...
		if(drv2605_read_reg(0x17)==0x5e)
		{
			am_util_stdio_printf("DRV2605 reg 0x17 readback is correct!\r\n");
		}
		else
		{
			am_util_stdio_printf("DRV2605 reg 0x17 readback is wrong!\r\n");
		}
		

    // 反馈控制：启用LRA，闭环控制
    drv2605_write_reg(0x1A, 0xB6);

    // 其他控制参数（推荐值）
    //drv2605_write_reg(0x1B, 0x93);
    //drv2605_write_reg(0x1C, 0xF5);
    //drv2605_write_reg(0x1D, 0xA3);
    //drv2605_write_reg(0x1E, 0x20);
    
}

// 播放一个简单波形（波形库编号1号，强度默认最大）
void drv2605_play_effect(void)
{
    drv2605_write_reg(0x04, 1); // 载入波形库第1个效果
    drv2605_write_reg(0x0C, 1); // GO = 1，启动播放
}

// 停止播放
void drv2605_stop(void)
{
    drv2605_write_reg(0x0C, 0);
}

// 完整初始化 + 启动播放
void drv2605_start_sequence(void)
{
    drv2605_init_lra();
    drv2605_play_effect();
}