#ifndef NAND_SPI_H
#define NAND_SPI_H

#include <stdint.h>
#include <stdbool.h>
#include "am_mcu_apollo.h"
#include "am_hal_mspi.h"
#include "am_util.h"
#include "flash.h"           // for g_Mspi2Handle and mspi2_init_spi_mode()
#include "storage_config.h"

#ifdef __cplusplus
extern "C" {
#endif

// ECC status reported by read operations
typedef enum {
    NAND_ECC_OK = 0,
    NAND_ECC_CORRECTED = 1,
    NAND_ECC_UNCORRECTABLE = 2
} nand_ecc_status_t;

// Initialize MSPI2 (SPI mode) if needed and reset NAND
int nand_init(void);

// Read JEDEC ID (at least Manufacturer ID and Device ID)
int nand_read_id(uint8_t *mid, uint8_t *did);

// Low-level primitives
int nand_reset(void);
int nand_write_enable(void);
int nand_get_status(uint8_t *status);


    // Internal ECC control (optional on-die ECC)
    int nand_set_ecc_enabled(bool enable);
    int nand_get_ecc_enabled(bool *enabled);

// Page/Block operations using page row address
int nand_block_erase(uint32_t block_idx);
int nand_page_read_to_cache(uint32_t page_row);
int nand_read_from_cache(uint16_t col, uint8_t *buf, uint32_t len, nand_ecc_status_t *ecc);
int nand_prog_load(uint16_t col, const uint8_t *buf, uint32_t len);
int nand_prog_exec(uint32_t page_row);

// Busy-wait until ready; returns 0 on ready, or <0 on error; outputs latest status and ECC interpretation
int nand_wait_ready(uint32_t timeout_ms, nand_ecc_status_t *ecc, uint8_t *status_out);

// Bad block handling (marker in spare[0])
int nand_is_bad(uint32_t block_idx);
int nand_mark_bad(uint32_t block_idx);

// Check if a subpage area is free (0xFF). Reads small probe window for speed.
int nand_is_subpage_free(uint32_t page_row, uint16_t subpage_col, uint32_t probe_len);

// Helpers to compute addressing
static inline uint32_t nand_block_to_page0(uint32_t block_idx) { return block_idx * NAND_PAGES_PER_BLOCK; }
static inline uint32_t nand_page_of(uint32_t block_idx, uint32_t page_in_block) { return block_idx * NAND_PAGES_PER_BLOCK + page_in_block; }

#ifdef __cplusplus
}
#endif

#endif // NAND_SPI_H

