#ifndef SI_SENSOR_H
#define SI_SENSOR_H

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdbool.h>
#include "am_util.h"
// #include "usart.h"

 #define DEBUG
 #ifdef DEBUG
     #define logi(format, ...) am_util_stdio_printf(format"\r\n",##__VA_ARGS__)
 #else
     #define logi(format, ...) 
 #endif

#define mdelay(ms)      am_util_delay_ms(ms)

#ifndef ARR_SIZE
#define ARR_SIZE(arr)                 (sizeof(arr)/sizeof(arr[0]))
#endif /* ARR_SIZE */

#define SIP_ASSERT(exp) do { \
    int res = exp;\
    if (res < 0) { \
        logi("error code = %d,line = %d", res,__LINE__); \
        return -1;\
    } \
} while (0)

#define SIP_VERIFY2(exp, format, ...) do { \
    if (!(exp)) { \
        logi(format, ##__VA_ARGS__); \
        return -1;\
    } \
} while (0)

typedef struct 
{
    struct {
        uint8_t         is_dri;         //inte mode
        uint8_t         gain_count;
        float           short_inte_time; // ms
        float           long_inte_time; // ms
    } conf;
    
    struct {            
        uint16_t        raw[2]; // channels
        float           lux_raw;
        float           ir_ratio;
        float           lux;
    } out;

    struct {
        //classify 
        float ch0_coef_c;
        float ch1_coef_c;
        float ch0_coef_ad;
        float ch1_coef_ad;
        float ch0_coef_h;
        float ch1_coef_h;
        float ratio_spec_1;
        float ratio_spec_2;
        float light_ch0_coef;
        float light_ch1_coef;        
        float light_lux_coef;
        
        //lux offset
        float offset_a_c;
        float offset_b_c;
        float offset_c_c;
        float offset_a_ad;
        float offset_b_ad;
        float offset_c_ad;
        float offset_a_h;
        float offset_b_h;
        float offset_c_h;
    } cali;

    struct {
        float           inte_time; // ms
        uint16_t        max_range; // count
        uint8_t         gain0_count;
        float           tgain0;
        float           cpl0;
        uint32_t        brightness;
    } tmp;

    struct {
        uint8_t         data_valid;
        uint8_t         data_invalid_times;
        uint8_t         int_status;
        uint8_t         enable_status;
        uint8_t         enabled;
        uint8_t         first_frame_flag_after_enable;
        uint8_t         data_changed;
    } stat;
} sip_als_info_t;

typedef struct {
    int8_t (*write_reg)(uint8_t addr, uint8_t value);
    int8_t (*write_regs)(uint8_t addr, uint8_t *p_value, uint8_t count);
    int8_t (*read_reg)(uint8_t addr, uint8_t *value);
    int8_t (*read_regs)(uint8_t addr, uint8_t *p_value, uint8_t count);
    int8_t (*modify_reg)(uint8_t addr, uint8_t value, uint8_t mask);
    int8_t (*check_reg)(uint8_t addr, uint8_t value, uint8_t mask);
} sip_i2c_info_t;

struct cali_data
{
    float cali_data[8];
};

typedef struct
{
    uint8_t reg;
    uint8_t value;
} sip_reg_t;

int8_t sip_write_regs(uint8_t reg, uint8_t *values, uint8_t count);
int8_t sip_write_reg(uint8_t reg, uint8_t values);
int8_t sip_read_regs(uint8_t reg, uint8_t *data, uint8_t r_size);
int8_t sip_read_reg(uint8_t reg, uint8_t *data);
int8_t sip_modify_reg(uint8_t reg, uint8_t value, uint8_t mask);
void sip_attach_address(uint8_t reg);
#endif