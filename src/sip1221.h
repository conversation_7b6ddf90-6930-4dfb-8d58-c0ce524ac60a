#ifndef SIP1221_H
#define SIP1221_H
#include <stdint.h>
#include "si_sensor.h"

#define CHIP_NAME        "SIP1221LR1S"
#define I2C_BUS  2
#define SIP_DEVICE_ADDR  0x58
#define SIP1221_ID       0x06
// reg list
#define SIP1221_CLK_CTRL 0x02
#define SIP1221_CTRL 0x05
#define SIP1221_INT_CTRL 0x06
#define SIP1221_ALS_ENABLE 0x50
#define SIP1221_ALS_CTRL0 0x51
#define SIP1221_ALS_CTRL1 0x52
#define SIP1221_ALS_INTEN 0x53
#define SIP1221_ALS_THLOW_H 0x54
#define SIP1221_ALS_THLOW_L 0x55
#define SIP1221_ALS_THHIGH_H 0x56
#define SIP1221_ALS_THHIGH_L 0x57
#define SIP1221_ALS_INTE_TIME_H 0x58
#define SIP1221_ALS_INTE_TIME_M 0x59
#define SIP1221_ALS_INTE_TIME_L 0x5A
#define SIP1221_ALS_PERIOD_STEP 0x5B
#define SIP1221_ALS_PERIOD_TIME 0x5C
#define SIP1221_ALS_RST_NUM 0x5D
#define SIP1221_ALS_AZ_CTRL 0x5E
#define SIP1221_ALS_AZ_EN 0x5F
#define SIP1221_RESERVE1 0x60
#define SIP1221_ALS0_1_GAIN 0x62
#define SIP1221_ALS2_GAIN 0x63
#define SIP1221_FLK_CTRL 0x71
#define SIP1221_FLK_INTE_H 0x73
#define SIP1221_FLK_INTE_M 0x74
#define SIP1221_FLK_INTE_L 0x75
#define SIP1221_ALS_INT_STATUS 0x81
#define SIP1221_DATA_VALID 0x84
#define SIP1221_ALS_DATA  0x90
#define SIP1221_ALSDATA_L 0x94
#define SIP1221_ALSDATA_H 0x95
#define SIP1221_WBDATA_L 0x96
#define SIP1221_WBDATA_H 0x97
#define SIP1221_FDATA 0x9F
#define SIP1221_FSTATUS 0xA0
#define SIP1221_FLVL 0xA1
#define SIP1221_F_THRESH 0xA2
#define SIP1221_FMODE0 0xA3
#define SIP1221_FMODE1 0xA4
#define SIP1221_SYNCDLY_CNT_H 0xE2
#define SIP1221_SYNCDLY_CNT_L 0xE3
#define SIP1221_SYNCTRIG_CNT 0xE4
#define SIP1221_SYNCWDT_CNT_H 0xE8
#define SIP1221_SYNC_FRQCHG_THRESH_H 0xEA
#define SIP1221_SYNC_FRQCHG_THRESH_L 0xEB
#define SIP1221_SYNC_PERIOD_L 0xEC
#define SIP1221_SYNC_PERIOD_M 0xED
#define SIP1221_SYNC_PERIOD_H 0xEE

//0x53 ALS_INT_EN
#define INT_EN      0x01<<2
#define ALS_ERR_INT_EN  0x01<<3
#define ALS_ANA_SAT_INT_EN  0x01<<2
#define ALS_DIG_SAT_EN  0x01<<1
#define ALS_INT_EN  0x01<<0

// als int status
#define FIFO_INT  0x01<<7
#define SYNC_CHG_INT  0x01<<6
#define SYNC_LOST_INT  0x01<<5
#define FLK_SAT_INT  0x01<<4
#define ALS_ERR_INT  0x01<<3
#define ALS_ANA_SAT_INT  0x01<<2
#define ALS_DIG_SAT_INT  0x01<<1
#define ALS_INT  0x01<<0

//threhold offset
#define HIGH_THREHOLD_OFFSET  5
#define LOW_THREHOLD_OFFSET  5

// bit operation
#define OSC_EN (0x01 << 1)
#define FLK_EN (0x01 << 5)
#define AL1_EN (0x01 << 4)
#define AL0_EN (0x01 << 3)
#define ALS_EN (0x01 << 0)
#define ALS_EN_ALL (0x19 << 0)
#define ALS_DATA_VALID (0x01 << 2)

#define ATIME_DUTY_CYCLE (2.716)
#define ATIME_DIG_DUTY_CYCLE (2.78)
#define ALS_INTE_TIME_MS(ms)     (((uint32_t)ms/ ATIME_DUTY_CYCLE) + 1)
#define MAX_ALS_VALUE 65535

//auto gain
#define LOW_AUTO_GAIN_VALUE 3
#define AUTO_GAIN_DIV 2
#define SATURATION_LOW_PERCENT 0.8
#define SATURATION_HIGH_PERCENT 1
#define LOWEST_GAIN_ID 0
#define INTEGER_SIZE 12
#define MAX_GAIN_ID 12

#define G_GAIN_MASK (0x0F << 4)
#define W_GAIN_MASK (0x0F << 0)

#define GAIN_0_25x  (0 << 4 | 0)
#define GAIN_0_5x   (1 << 4 | 1)
#define GAIN_1x     (2 << 4 | 2)
#define GAIN_2x     (3 << 4 | 3)
#define GAIN_4x     (4 << 4 | 4)
#define GAIN_8x     (5 << 4 | 5)
#define GAIN_16x    (6 << 4 | 6)
#define GAIN_32x    (7 << 4 | 7)
#define GAIN_64x    (8 << 4 | 8)
#define GAIN_128x   (9 << 4 | 9)
#define GAIN_256x   (10 << 4| 10)
#define GAIN_512x   (11 << 4| 11)
#define GAIN_1024x  (12 << 4| 12)
//flicker
#define Flicker_ITIME 0.992f  //ms, flicker inte-time,
#define FLICKER_SAMPLE_RATE (1000)
#define FFT_MAX_COUNT   256

#define MAX_FIFO_BYTES (512)  // max = 512, and must > ALS_BUF_SIZE
#define MAX_FIFO_SIZE (MAX_FIFO_BYTES / 2)
#define MAX_FLICKER_REPORT_SIZE (16)

#define EPS             1e-7
#define FLOAT_LESS_EQU(a, b) (((a) - (b)) < (EPS))
#define FLOAT_MORE_EQU(a, b) (((a) - (b)) > (-EPS))
#define FLOAT_EQU(a,b)       (abs((a) - (b)) <= EPS)

// sip1328_FIFO_STATUS @0xA0
#define FIFO_LVL_L (0x01 << 5)
#define FIFO_FULL (0x01 << 4)
#define FIFO_EMPTY (0x01 << 3)
#define FIFO_AF_INT (0x01 << 2)
#define FIFO_AE_INT (0x01 << 1)


//fifomode
#define FIFO_MODE   (0x01 <<3)
#define max(a,b) (((a) > (b)) ? (a) : (b))
#define min(a,b) (((a) < (b)) ? (a) : (b))

#define SIP1221_SUPPORT_DUMP        0
#define SIP1221_LUX_OFFSET          1
struct sip1221_device 
{
    sip_i2c_info_t iinfo;
    sip_als_info_t ainfo;
};

typedef struct 
{
    float tgain;
    float rgain;
} als_gain_map;


int sip1221_sensor_init(void);
int sip1221_als_enable(void);
int sip1221_als_disable(void);
int sip1221_als_factory_enable(void);
int sip1221_als_factory_disable(void);
int sip1221_updata_als_cali(float *data,int len);
int sip1221_updata_backlight(uint16_t level);
int sip1221_als_sample(float *lux);
int sip1221_als_factory_sample(uint16_t *ch0_raw,uint16_t *ch1_raw);
int sip1221_als_int_sample(float *lux);
#endif