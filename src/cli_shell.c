#include <string.h>
#include <stdio.h>
#include "FreeRTOS.h"
#include "task.h"
#include "FreeRTOS_CLI.h"
#include "am_util.h"
#include "fs_service.h"
#include "uart.h"

// Use ISR-provided input buffer from uart.c
extern volatile uint32_t g_uart_rx_len;
extern uint8_t g_uart_rx_data[];

#define CLI_INPUT_MAX   256
#define CLI_OUTPUT_MAX  512
#define CLI_TASK_STACK  2048
#define CLI_TASK_PRIO   (tskIDLE_PRIORITY + 1)

// Utilities
static const char *skip_ws(const char *s) { while (*s==' '||*s=='\t') s++; return s; }

// --------------------- Command: mount ---------------------
static BaseType_t cmd_mount(char *out, size_t outlen, const char *cmd)
{
    (void)cmd;
    int r = fs_service_mount(false);
    if (r == 0) snprintf(out, outlen, "Mounted.\r\n");
    else snprintf(out, outlen, "mount failed: %d\r\n", r);
    return pdFALSE;
}
static const CLI_Command_Definition_t def_mount = {
    .pcCommand = "mount",
    .pcHelpString = "mount: mount filesystem\r\n",
    .pxCommandInterpreter = cmd_mount,
    .cExpectedNumberOfParameters = 0
};

// --------------------- Command: umount ---------------------
static BaseType_t cmd_umount(char *out, size_t outlen, const char *cmd)
{
    (void)cmd;
    int r = fs_service_unmount();
    if (r == 0) snprintf(out, outlen, "Unmounted.\r\n");
    else snprintf(out, outlen, "umount failed: %d\r\n", r);
    return pdFALSE;
}
static const CLI_Command_Definition_t def_umount = {
    .pcCommand = "umount",
    .pcHelpString = "umount: unmount filesystem\r\n",
    .pxCommandInterpreter = cmd_umount,
    .cExpectedNumberOfParameters = 0
};

// --------------------- Command: mkfs ---------------------
static BaseType_t cmd_mkfs(char *out, size_t outlen, const char *cmd)
{
    (void)cmd;
    int r = fs_service_format();
    if (r != 0) { snprintf(out, outlen, "mkfs failed: %d\r\n", r); return pdFALSE; }
    r = fs_service_mount(false);
    snprintf(out, outlen, (r==0)?"mkfs+mount ok\r\n":"mount failed: %d\r\n", r);
    return pdFALSE;
}
static const CLI_Command_Definition_t def_mkfs = {
    .pcCommand = "mkfs",
    .pcHelpString = "mkfs: format device and mount\r\n",
    .pxCommandInterpreter = cmd_mkfs,
    .cExpectedNumberOfParameters = 0
};

// --------------------- Command: df ---------------------
static BaseType_t cmd_df(char *out, size_t outlen, const char *cmd)
{
    (void)cmd;
    struct lfs_fsinfo info; lfs_ssize_t used = -1;
    int r = fs_service_fsinfo(&info, &used);
    if (r != 0) { snprintf(out, outlen, "df failed: %d\r\n", r); return pdFALSE; }
    unsigned long long total_b = (unsigned long long)info.block_size * info.block_count;
    unsigned long long used_b = (used >= 0) ? (unsigned long long)info.block_size * (unsigned long long)used : 0ULL;
    unsigned long long free_b = (total_b >= used_b) ? (total_b - used_b) : 0ULL;
    snprintf(out, outlen,
             "block_size=%lu, blocks=%lu, used_blocks=%ld\r\nbytes: total=%llu, used=%llu, free=%llu\r\n",
             (unsigned long)info.block_size, (unsigned long)info.block_count, (long)used,
             total_b, used_b, free_b);
    return pdFALSE;
}
static const CLI_Command_Definition_t def_df = {
    .pcCommand = "df",
    .pcHelpString = "df: show filesystem usage\r\n",
    .pxCommandInterpreter = cmd_df,
    .cExpectedNumberOfParameters = 0
};

// --------------------- Command: ls [path] ---------------------
static BaseType_t cmd_ls(char *out, size_t outlen, const char *cmd)
{
    static lfs_dir_t dir; static int active = 0; static char path[128];
    const char *p = strchr(cmd, ' ');
    if (!active) {
        const char *arg = NULL; BaseType_t arglen = 0;
        if (p) { arg = FreeRTOS_CLIGetParameter(cmd, 1, &arglen); }
        if (arg && arglen > 0) {
            size_t n = (arglen < sizeof(path)-1) ? (size_t)arglen : sizeof(path)-1;
            memcpy(path, arg, n); path[n] = 0;
        } else {
            strcpy(path, "/");
        }
        fs_acquire();
        int r = lfs_dir_open(fs_get_lfs(), &dir, path);
        if (r != 0) { fs_release(); snprintf(out, outlen, "ls: open '%s' err %d\r\n", path, r); return pdFALSE; }
        active = 1;
        snprintf(out, outlen, "Listing %s:\r\n", path);
        return pdTRUE;
    }
    // emit entries until buffer near full or done
    size_t used = strlen(out);
    struct lfs_info info;
    while (used + 64 < outlen) {
        int r = lfs_dir_read(fs_get_lfs(), &dir, &info);
        if (r == 0) { lfs_dir_close(fs_get_lfs(), &dir); fs_release(); active = 0; break; }
        if (r < 0)  { lfs_dir_close(fs_get_lfs(), &dir); fs_release(); active = 0; snprintf(out+used, outlen-used, "\r\n(error %d)\r\n", r); return pdFALSE; }
        const char *t = (info.type == LFS_TYPE_DIR) ? "<DIR>" : "     ";
        used += (size_t)snprintf(out+used, outlen-used, "%s %lu %s\r\n", t, (unsigned long)info.size, info.name);
    }
    return active ? pdTRUE : pdFALSE;
}
static const CLI_Command_Definition_t def_ls = {
    .pcCommand = "ls",
    .pcHelpString = "ls [path]: list directory (default '/')\r\n",
    .pxCommandInterpreter = cmd_ls,
    .cExpectedNumberOfParameters = -1
};

// --------------------- Command: cat <path> ---------------------
static BaseType_t cmd_cat(char *out, size_t outlen, const char *cmd)
{
    static lfs_file_t file; static int active=0; static size_t remain=0; static char path[128];
    if (!active) {
        BaseType_t len; const char *arg = FreeRTOS_CLIGetParameter(cmd, 1, &len);
        if (!arg || len<=0) { snprintf(out, outlen, "usage: cat <path>\r\n"); return pdFALSE; }
        size_t n = (len < sizeof(path)-1)?(size_t)len:sizeof(path)-1; memcpy(path,arg,n); path[n]=0;
        fs_acquire();
        int r = lfs_file_open(fs_get_lfs(), &file, path, LFS_O_RDONLY);
        if (r!=0) { fs_release(); snprintf(out, outlen, "cat: open '%s' err %d\r\n", path, r); return pdFALSE; }
        remain = (size_t)lfs_file_size(fs_get_lfs(), &file);
        active = 1; out[0]='\0';
        return pdTRUE;
    }
    size_t chunk = (remain > outlen-4) ? (outlen-4) : remain;
    if (chunk) {
        lfs_ssize_t rd = lfs_file_read(fs_get_lfs(), &file, out, (lfs_size_t)chunk);
        if (rd < 0) { snprintf(out, outlen, "\r\n(read err %ld)\r\n", (long)rd); lfs_file_close(fs_get_lfs(), &file); fs_release(); active=0; return pdFALSE; }
        remain -= (size_t)rd;
        if (remain == 0) { lfs_file_close(fs_get_lfs(), &file); fs_release(); active=0; }
        return active ? pdTRUE : pdFALSE;
    } else {
        lfs_file_close(fs_get_lfs(), &file); fs_release(); active=0; out[0]='\0'; return pdFALSE;
    }
}
static const CLI_Command_Definition_t def_cat = {
    .pcCommand = "cat",
    .pcHelpString = "cat <path>: print file contents\r\n",
    .pxCommandInterpreter = cmd_cat,
    .cExpectedNumberOfParameters = 1
};

// --------------------- Command: write <path> <text> ---------------------
static BaseType_t cmd_write_common(char *out, size_t outlen, const char *cmd, bool append)
{
    BaseType_t len; const char *path = FreeRTOS_CLIGetParameter(cmd, 1, &len);
    if (!path || len<=0) { snprintf(out, outlen, "usage: %s <path> <text>\r\n", append?"append":"write"); return pdFALSE; }
    char pbuf[128]; size_t pn=(len<sizeof(pbuf)-1)?(size_t)len:sizeof(pbuf)-1; memcpy(pbuf,path,pn); pbuf[pn]=0;
    // Find start of text: skip command + space + path; then skip one space
    const char *after = strstr(cmd, pbuf);
    if (!after) { snprintf(out,outlen,"internal parse error\r\n"); return pdFALSE; }
    after += strlen(pbuf);
    after = skip_ws(after);
    if (*after=='\0') { snprintf(out,outlen,"no text given\r\n"); return pdFALSE; }
    lfs_file_t f; int flags = LFS_O_WRONLY | LFS_O_CREAT | (append?LFS_O_APPEND:LFS_O_TRUNC);
    fs_acquire();
    int r = lfs_file_open(fs_get_lfs(), &f, pbuf, flags);
    if (r!=0) { fs_release(); snprintf(out,outlen,"open err %d\r\n", r); return pdFALSE; }
    lfs_ssize_t wr = lfs_file_write(fs_get_lfs(), &f, after, (lfs_size_t)strlen(after));
    lfs_file_close(fs_get_lfs(), &f);
    fs_release();
    if (wr < 0) snprintf(out,outlen,"write err %ld\r\n", (long)wr);
    else snprintf(out,outlen,"%ld bytes written\r\n", (long)wr);
    return pdFALSE;
}
static BaseType_t cmd_write(char *out, size_t outlen, const char *cmd){return cmd_write_common(out,outlen,cmd,false);} 
static BaseType_t cmd_append(char *out, size_t outlen, const char *cmd){return cmd_write_common(out,outlen,cmd,true);} 
static const CLI_Command_Definition_t def_write = {
    .pcCommand = "write",
    .pcHelpString = "write <path> <text>: create/truncate and write text\r\n",
    .pxCommandInterpreter = cmd_write,
    .cExpectedNumberOfParameters = -1
};
static const CLI_Command_Definition_t def_append = {
    .pcCommand = "append",
    .pcHelpString = "append <path> <text>: append text\r\n",
    .pxCommandInterpreter = cmd_append,
    .cExpectedNumberOfParameters = -1
};

// --------------------- Command: rm <path> ---------------------
static BaseType_t cmd_rm(char *out, size_t outlen, const char *cmd)
{
    BaseType_t len; const char *path = FreeRTOS_CLIGetParameter(cmd, 1, &len);
    if (!path || len<=0) { snprintf(out, outlen, "usage: rm <path>\r\n"); return pdFALSE; }
    char pbuf[128]; size_t pn=(len<sizeof(pbuf)-1)?(size_t)len:sizeof(pbuf)-1; memcpy(pbuf,path,pn); pbuf[pn]=0;
    fs_acquire(); int r = lfs_remove(fs_get_lfs(), pbuf); fs_release();
    if (r==0) snprintf(out,outlen,"removed\r\n"); else snprintf(out,outlen,"rm err %d\r\n", r);
    return pdFALSE;
}
static const CLI_Command_Definition_t def_rm = {
    .pcCommand = "rm",
    .pcHelpString = "rm <path>: remove file\r\n",
    .pxCommandInterpreter = cmd_rm,
    .cExpectedNumberOfParameters = 1
};

// --------------------- Command: mkdir <path> ---------------------
static BaseType_t cmd_mkdir(char *out, size_t outlen, const char *cmd)
{
    BaseType_t len; const char *path = FreeRTOS_CLIGetParameter(cmd, 1, &len);
    if (!path || len<=0) { snprintf(out, outlen, "usage: mkdir <path>\r\n"); return pdFALSE; }
    char pbuf[128]; size_t pn=(len<sizeof(pbuf)-1)?(size_t)len:sizeof(pbuf)-1; memcpy(pbuf,path,pn); pbuf[pn]=0;
    fs_acquire(); int r = lfs_mkdir(fs_get_lfs(), pbuf); fs_release();
    if (r==0) snprintf(out,outlen,"mkdir ok\r\n"); else snprintf(out,outlen,"mkdir err %d\r\n", r);
    return pdFALSE;
}
static const CLI_Command_Definition_t def_mkdir = {
    .pcCommand = "mkdir",
    .pcHelpString = "mkdir <path>: create directory\r\n",
    .pxCommandInterpreter = cmd_mkdir,
    .cExpectedNumberOfParameters = 1
};

// --------------------- Command: stat <path> ---------------------
static BaseType_t cmd_stat(char *out, size_t outlen, const char *cmd)
{
    BaseType_t len; const char *path = FreeRTOS_CLIGetParameter(cmd, 1, &len);
    if (!path || len<=0) { snprintf(out, outlen, "usage: stat <path>\r\n"); return pdFALSE; }
    char pbuf[128]; size_t pn=(len<sizeof(pbuf)-1)?(size_t)len:sizeof(pbuf)-1; memcpy(pbuf,path,pn); pbuf[pn]=0;
    struct lfs_info info; fs_acquire(); int r = lfs_stat(fs_get_lfs(), pbuf, &info); fs_release();
    if (r!=0) { snprintf(out,outlen,"stat err %d\r\n", r); return pdFALSE; }
    snprintf(out,outlen,"%s %lu %s\r\n", (info.type==LFS_TYPE_DIR)?"DIR":"FILE", (unsigned long)info.size, info.name);
    return pdFALSE;
}
static const CLI_Command_Definition_t def_stat = {
    .pcCommand = "stat",
    .pcHelpString = "stat <path>: show type and size\r\n",
    .pxCommandInterpreter = cmd_stat,
    .cExpectedNumberOfParameters = 1
};

static void register_commands(void)
{
    FreeRTOS_CLIRegisterCommand(&def_mount);
    FreeRTOS_CLIRegisterCommand(&def_umount);
    FreeRTOS_CLIRegisterCommand(&def_mkfs);
    FreeRTOS_CLIRegisterCommand(&def_df);
    FreeRTOS_CLIRegisterCommand(&def_ls);
    FreeRTOS_CLIRegisterCommand(&def_cat);
    FreeRTOS_CLIRegisterCommand(&def_write);
    FreeRTOS_CLIRegisterCommand(&def_append);
    FreeRTOS_CLIRegisterCommand(&def_rm);
    FreeRTOS_CLIRegisterCommand(&def_mkdir);
    FreeRTOS_CLIRegisterCommand(&def_stat);
}

static void cli_task(void *arg)
{
    (void)arg;

    // Initialize NAND/Dhara/LittleFS config (on-die ECC enabled by default)
    int hw = fs_service_hw_init(true);
    if (hw != 0) {
        am_util_stdio_printf("FS HW init failed: %d\r\n", hw);
    }

    register_commands();
    am_util_stdio_printf("\r\nCLI ready. Type 'help' for commands.\r\n> ");

    char input[CLI_INPUT_MAX]; char output[CLI_OUTPUT_MAX];
    for (;;) {
        if (g_uart_rx_len > 0) {
            // Copy line and clear flag
            size_t n = (g_uart_rx_len < sizeof(input)-1) ? g_uart_rx_len : sizeof(input)-1;
            memcpy(input, g_uart_rx_data, n); input[n] = 0;
            g_uart_rx_len = 0;

            // Trim CR/LF
            char *e = input + strlen(input);
            while (e>input && (e[-1]=='\r' || e[-1]=='\n')) { *--e = 0; }

            // Process; FreeRTOS_CLIProcessCommand may need multiple calls
            BaseType_t more;
            do {
                output[0] = 0;
                more = FreeRTOS_CLIProcessCommand(input, output, sizeof(output));
                if (output[0]) am_util_stdio_printf("%s", output);
            } while (more != pdFALSE);
            am_util_stdio_printf("\r\n> ");
        } else {
            vTaskDelay(pdMS_TO_TICKS(10));
        }
    }
}

void cli_shell_start(void)
{
    xTaskCreate(cli_task, "cli", CLI_TASK_STACK/sizeof(StackType_t), NULL, CLI_TASK_PRIO, NULL);
}

