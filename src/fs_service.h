#ifndef FS_SERVICE_H
#define FS_SERVICE_H

#include <stdbool.h>
#include "../external/littlefs/lfs.h"
#include "../external/dhara/dhara/map.h"

#ifdef __cplusplus
extern "C" {
#endif

// Initialize MSPI2/NAND and Dhara geometry. Also configures on-die ECC
// if enable_ecc is true.
int fs_service_hw_init(bool enable_ecc);

// Mount LittleFS over Dhara, or format then mount if needed when
// format_if_needed is true. Returns 0 on success.
int fs_service_mount(bool format_if_needed);

// Unmount and format helpers (mutex-guarded)
int fs_service_unmount(void);
int fs_service_format(void);

// Filesystem info (block geometry and used blocks)
int fs_service_fsinfo(struct lfs_fsinfo *info, lfs_ssize_t *used_blocks);

// Optional explicit locking for batching multiple LFS calls atomically
void fs_acquire(void);
void fs_release(void);

// Accessors to LittleFS context
const struct lfs_config *fs_get_lfs_cfg(void);
lfs_t *fs_get_lfs(void);
struct dhara_map *fs_get_dhara_map(void);

#ifdef __cplusplus
}
#endif

#endif // FS_SERVICE_H

