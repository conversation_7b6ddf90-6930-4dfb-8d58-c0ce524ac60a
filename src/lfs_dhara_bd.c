#include <string.h>
#include "lfs_dhara_bd.h"
#include "../external/dhara/dhara/map.h"
#include "../external/littlefs/lfs.h"

typedef struct {
    struct dhara_map *map;
} lfs_dhara_ctx_t;

static int lfs_bd_read(const struct lfs_config *c, lfs_block_t block,
                       lfs_off_t off, void *buffer, lfs_size_t size) {
    lfs_dhara_ctx_t *ctx = (lfs_dhara_ctx_t*)c->context;
    uint8_t *dst = (uint8_t*)buffer;

    // Expect 512-byte sectors and aligned accesses
    while (size > 0) {
        if ((off % (1u << c->read_size)) != 0) {
            // Not aligned; littlefs shouldn't do this with our config
            return LFS_ERR_INVAL;
        }
        uint32_t sector = (uint32_t)block + (uint32_t)(off / c->read_size);
        if (dhara_map_read(ctx->map, sector, dst, NULL) < 0) return LFS_ERR_IO;
        dst += c->read_size;
        off += c->read_size;
        size -= c->read_size;
    }
    return 0;
}

static int lfs_bd_prog(const struct lfs_config *c, lfs_block_t block,
                       lfs_off_t off, const void *buffer, lfs_size_t size) {
    lfs_dhara_ctx_t *ctx = (lfs_dhara_ctx_t*)c->context;
    const uint8_t *src = (const uint8_t*)buffer;

    while (size > 0) {
        if ((off % (1u << c->prog_size)) != 0) return LFS_ERR_INVAL;
        uint32_t sector = (uint32_t)block + (uint32_t)(off / c->prog_size);
        if (dhara_map_write(ctx->map, sector, src, NULL) < 0) return LFS_ERR_IO;
        src += c->prog_size;
        off += c->prog_size;
        size -= c->prog_size;
    }
    return 0;
}

static int lfs_bd_erase(const struct lfs_config *c, lfs_block_t block) {
    lfs_dhara_ctx_t *ctx = (lfs_dhara_ctx_t*)c->context;
    // Hint to Dhara that this sector is no longer used.
    // Our block_size == sector size, so trim one sector.
    (void)ctx;
    (void)block;
    // Using trim is optional; many run LittleFS with erase as a no-op on FTL.
    return 0;
}

static int lfs_bd_sync(const struct lfs_config *c) {
    lfs_dhara_ctx_t *ctx = (lfs_dhara_ctx_t*)c->context;
    if (dhara_map_sync(ctx->map, NULL) < 0) return LFS_ERR_IO;
    return 0;
}

void lfs_dhara_default_config(struct lfs_config *cfg, struct dhara_map *map) {
    static lfs_dhara_ctx_t s_ctx; // one instance; safe for single FS
    memset(cfg, 0, sizeof(*cfg));

    s_ctx.map = map;
    cfg->context       = &s_ctx;

    // Geometry: expose an LBA-like device with 512B sectors
    cfg->read  = lfs_bd_read;
    cfg->prog  = lfs_bd_prog;
    cfg->erase = lfs_bd_erase; // no-op for FTL-backed devices
    cfg->sync  = lfs_bd_sync;

    cfg->read_size   = 512; // must equal Dhara sector size
    cfg->prog_size   = 512;
    cfg->block_size  = 512; // present 1 sector per "block"

    // Capacity from Dhara
    const lfs_size_t sectors = (lfs_size_t)dhara_map_capacity(map);
    cfg->block_count = sectors;

    // Caches and lookahead: tune as needed
    cfg->cache_size     = 512;            // one sector
    cfg->lookahead_size = 128;            // 128 sectors bitmap
    cfg->block_cycles   = -1;             // disable LFS wear-leveling; Dhara handles it
}

