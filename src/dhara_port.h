#ifndef DHARA_PORT_H
#define DHARA_PORT_H

#include <stdint.h>
#include "../external/dhara/dhara/nand.h"

#ifdef __cplusplus
extern "C" {
#endif

// Global Dhara NAND descriptor initialized by dhara_port_init()
extern struct dhara_nand g_dhara_nand;

// Initialize Dhara geometry from storage_config.h values
// Returns 0 on success, <0 on error (e.g., invalid geometry)
int dhara_port_init(void);

#ifdef __cplusplus
}
#endif

#endif // DHARA_PORT_H

