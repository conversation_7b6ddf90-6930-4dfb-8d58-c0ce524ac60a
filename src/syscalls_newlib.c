// Minimal newlib syscall stubs for bare-metal build
// Provides _sbrk, _write, and friends so we can link without semihosting

#include <sys/types.h>
#include <sys/stat.h>
#include <errno.h>
#include <stdint.h>
#include <unistd.h>

// Linker-provided symbols from linker_script.ld
extern uint8_t __heap_start__;
extern uint8_t __HeapLimit; // alias to __heap_end__

// Simple bump-pointer heap
void* _sbrk(ptrdiff_t incr) {
    static uint8_t *cur = &__heap_start__;
    uint8_t *limit = &__HeapLimit;
    uint8_t *prev = cur;
    if (incr < 0) {
        if ((ptrdiff_t)(cur - &__heap_start__) + incr < 0) {
            errno = ENOMEM;
            return (void *)-1;
        }
        cur = (uint8_t *)((ptrdiff_t)cur + incr);
        return prev;
    }
    if (cur + incr > limit) {
        errno = ENOMEM;
        return (void *)-1;
    }
    cur += incr;
    return prev;
}

int _close(int fd) {
    (void)fd;
    errno = ENOSYS;
    return -1;
}

int _fstat(int fd, struct stat *st) {
    (void)fd;
    if (!st) { errno = EINVAL; return -1; }
    st->st_mode = S_IFCHR;
    return 0;
}

int _isatty(int fd) {
    (void)fd;
    return 1;
}

off_t _lseek(int fd, off_t offset, int whence) {
    (void)fd; (void)offset; (void)whence;
    errno = ENOSYS;
    return (off_t)-1;
}

ssize_t _read(int fd, void *buf, size_t count) {
    (void)fd; (void)buf; (void)count;
    errno = ENOSYS;
    return -1;
}

ssize_t _write(int fd, const void *buf, size_t count) {
    (void)fd; (void)buf;
    // Sink output; return success to avoid aborts
    return (ssize_t)count;
}

int _getpid(void) {
    return 1;
}

int _kill(int pid, int sig) {
    (void)pid; (void)sig;
    errno = ENOSYS;
    return -1;
}

void _exit(int status) {
    (void)status;
    // Trap here in case of unexpected exit
    while (1) { __asm__ volatile("wfi"); }
}

