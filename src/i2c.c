// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the
//   use or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------

#include "i2c.h"

void* g_pIOMHandle_0 = NULL;  // for I2C0
void* g_pIOMHandle_1 = NULL;  // for I2C1
void* g_pIOMHandle_2 = NULL;  // for I2C2
void* g_pIOMHandle_3 = NULL;  // for I2C3
void* g_pIOMHandle_7 = NULL;  // for I2C7

am_hal_iom_config_t g_I2CConfig = {
    .eInterfaceMode = AM_HAL_IOM_I2C_MODE,
    .ui32ClockFreq = I2C_SPEED,
    .eSpiMode = AM_HAL_IOM_SPI_MODE_0,  // Not used in I2C
    .pNBTxnBuf = NULL,
    .ui32NBTxnBufLength = 0,
};

void init_i2c(uint32_t I2C_MODULE, void** g_pIOMHandle, uint32_t I2C_SCL_PIN,
              uint32_t I2C_SDA_PIN) {
  am_hal_iom_initialize(I2C_MODULE, g_pIOMHandle);
  am_hal_iom_power_ctrl(*g_pIOMHandle, AM_HAL_SYSCTRL_WAKE, false);
  am_hal_iom_configure(*g_pIOMHandle, &g_I2CConfig);
  am_hal_iom_enable(*g_pIOMHandle);

  // IO??
  if (I2C_MODULE == I2C0_MODULE) {
    am_hal_gpio_pinconfig(I2C_SCL_PIN, g_AM_BSP_GPIO_IOM0_SCL);
    am_hal_gpio_pinconfig(I2C_SDA_PIN, g_AM_BSP_GPIO_IOM0_SDA);
  } else if (I2C_MODULE == I2C1_MODULE) {
    am_hal_gpio_pinconfig(I2C_SCL_PIN, g_AM_BSP_GPIO_IOM1_SCL);
    am_hal_gpio_pinconfig(I2C_SDA_PIN, g_AM_BSP_GPIO_IOM1_SDA);
  } else if (I2C_MODULE == I2C2_MODULE) {
    am_hal_gpio_pinconfig(I2C_SCL_PIN, g_AM_BSP_GPIO_IOM2_SCL);
    am_hal_gpio_pinconfig(I2C_SDA_PIN, g_AM_BSP_GPIO_IOM2_SDA);
  } else if (I2C_MODULE == I2C3_MODULE) {
    am_hal_gpio_pinconfig(I2C_SCL_PIN, g_AM_BSP_GPIO_IOM3_SCL);
    am_hal_gpio_pinconfig(I2C_SDA_PIN, g_AM_BSP_GPIO_IOM3_SDA);
  } else if (I2C_MODULE == I2C7_MODULE) {
    am_hal_gpio_pinconfig(I2C_SCL_PIN, g_AM_BSP_GPIO_IOM7_SCL);
    am_hal_gpio_pinconfig(I2C_SDA_PIN, g_AM_BSP_GPIO_IOM7_SDA);
  } else {
    am_util_stdio_printf("Wrong I2C moudle number!\r\n");
  }
}

void i2c_write_byte(void** g_pIOMHandle, uint8_t I2C_SLAVE_ADDR, uint8_t reg,
                    uint8_t value) {
  uint8_t buf[2] = {reg, value};

  am_hal_iom_transfer_t xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_TX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32TxBuffer = (uint32_t*)buf,
      .ui32NumBytes = 2,
      .bContinue = false,
      //.bRepeat = false,
  };

  am_hal_iom_blocking_transfer(*g_pIOMHandle, &xfer);
}

int32_t i2c_write_bytes(void** g_pIOMHandle, uint8_t I2C_SLAVE_ADDR,
                        uint8_t reg, uint8_t* data, uint32_t len) {
  uint8_t buf[256];
  if (len + 1 > sizeof(buf)) return -1;  // 防止越界

  buf[0] = reg;                // 第一个字节是寄存器地址
  memcpy(&buf[1], data, len);  // 后续为数据

  am_hal_iom_transfer_t xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_TX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32TxBuffer = (uint32_t*)buf,
      .ui32NumBytes = len + 1,
      .bContinue = false,
  };

  return am_hal_iom_blocking_transfer(*g_pIOMHandle, &xfer);
}

int32_t i2c_write_bytes_no_reg(void** g_pIOMHandle, uint8_t I2C_SLAVE_ADDR,
                               uint8_t* data, uint32_t len) {
  uint8_t buf[256];
  if (len + 1 > sizeof(buf)) return -1;  // 防止越界

  // buf[0] = reg;                         // 第一个字节是寄存器地址
  memcpy(buf, data, len);  // 后续为数据

  am_hal_iom_transfer_t xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_TX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32TxBuffer = (uint32_t*)buf,
      .ui32NumBytes = len,
      .bContinue = false,
  };

  return am_hal_iom_blocking_transfer(*g_pIOMHandle, &xfer);
}

int32_t i2c7_write_bytes_no_reg(uint8_t I2C_SLAVE_ADDR, uint8_t* data,
                                uint32_t len) {
  uint8_t buf[256];
  if (len + 1 > sizeof(buf)) return -1;  // 防止越界

  // buf[0] = reg;                         // 第一个字节是寄存器地址
  memcpy(buf, data, len);  // 后续为数据

  am_hal_iom_transfer_t xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_TX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32TxBuffer = (uint32_t*)buf,
      .ui32NumBytes = len,
      .bContinue = false,
  };

  return am_hal_iom_blocking_transfer(g_pIOMHandle_7, &xfer);
}

uint8_t i2c_read_byte(void** g_pIOMHandle, uint8_t I2C_SLAVE_ADDR,
                      uint8_t reg) {
  uint8_t result = 0;

  // Step 1: ????????
  am_hal_iom_transfer_t write_xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_TX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32TxBuffer = (uint32_t*)&reg,
      .ui32NumBytes = 1,
      .bContinue = true,  // ??? STOP(repeated start)
                          //.bRepeat = false,
  };

  am_hal_iom_blocking_transfer(*g_pIOMHandle, &write_xfer);

  // Step 2: ?????
  am_hal_iom_transfer_t read_xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_RX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32RxBuffer = (uint32_t*)&result,
      .ui32NumBytes = 1,
      .bContinue = false,
      //.bRepeat = false,
  };

  am_hal_iom_blocking_transfer(*g_pIOMHandle, &read_xfer);

  return result;
}

int32_t i2c_read_bytes(void** g_pIOMHandle, uint8_t I2C_SLAVE_ADDR, uint8_t reg,
                       uint8_t* data, uint32_t len) {
  // Step 1: 写入寄存器地址（不发 STOP）
  am_hal_iom_transfer_t write_xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_TX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32TxBuffer = (uint32_t*)&reg,
      .ui32NumBytes = 1,
      .bContinue = true,  // 生成 repeated START
  };
  int status = am_hal_iom_blocking_transfer(*g_pIOMHandle, &write_xfer);
  if (status != 0) return status;

  // Step 2: 读取数据
  am_hal_iom_transfer_t read_xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_RX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32RxBuffer = (uint32_t*)data,
      .ui32NumBytes = len,
      .bContinue = false,
  };
  return am_hal_iom_blocking_transfer(*g_pIOMHandle, &read_xfer);
}

int32_t i2c_read_bytes_no_reg(void** g_pIOMHandle, uint8_t I2C_SLAVE_ADDR,
                              uint8_t* data, uint32_t len) {
  // Step 1: 写入寄存器地址（不发 STOP）
  am_hal_iom_transfer_t write_xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_TX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32TxBuffer = NULL,
      .ui32NumBytes = 0,
      .bContinue = true,  // 生成 repeated START
  };
  int status = am_hal_iom_blocking_transfer(*g_pIOMHandle, &write_xfer);
  if (status != 0) return status;

  // Step 2: 读取数据
  am_hal_iom_transfer_t read_xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_RX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32RxBuffer = (uint32_t*)data,
      .ui32NumBytes = len,
      .bContinue = false,
  };
  return am_hal_iom_blocking_transfer(*g_pIOMHandle, &read_xfer);
}

int32_t i2c7_read_bytes_no_reg(uint8_t I2C_SLAVE_ADDR, uint8_t* data,
                               uint32_t len) {
  // Step 1: 写入寄存器地址（不发 STOP）
  am_hal_iom_transfer_t write_xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_TX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32TxBuffer = NULL,
      .ui32NumBytes = 0,
      .bContinue = true,  // 生成 repeated START
  };
  int status = am_hal_iom_blocking_transfer(g_pIOMHandle_7, &write_xfer);
  if (status != 0) return status;

  // Step 2: 读取数据
  am_hal_iom_transfer_t read_xfer = {
      .uPeerInfo.ui32I2CDevAddr = I2C_SLAVE_ADDR,
      .ui8Priority = 1,
      .eDirection = AM_HAL_IOM_RX,
      .ui32InstrLen = 0,
      .ui64Instr = 0,
      .pui32RxBuffer = (uint32_t*)data,
      .ui32NumBytes = len,
      .bContinue = false,
  };
  return am_hal_iom_blocking_transfer(g_pIOMHandle_7, &read_xfer);
}

uint8_t I2C_read_test(uint32_t I2C_MODULE, void** g_pIOMHandle,
                      uint32_t I2C_SCL_PIN, uint32_t I2C_SDA_PIN,
                      uint8_t I2C_SLAVE_ADDR, uint8_t reg, uint8_t read_value) {
  uint8_t I2C_read = 0;
  if ((*g_pIOMHandle) == NULL) {
    init_i2c(I2C_MODULE, g_pIOMHandle, I2C_SCL_PIN, I2C_SDA_PIN);
  }
  am_util_delay_ms(10);
  I2C_read = i2c_read_byte(g_pIOMHandle, I2C_SLAVE_ADDR,
                           reg);  // read the Interrupt configuration.
  if (I2C_read == read_value)     // should be 0x3d.
  {
    return 1;
  } else {
    am_util_stdio_printf("Expected value:%x, but read value:%x\r\n", read_value,
                         I2C_read);
    return 0;
  }
}