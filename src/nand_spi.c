#include "nand_spi.h"

// Internal helpers to construct MSPI PIO transfers safely
static uint32_t mspi_pio_write(uint8_t instr, bool send_addr, uint32_t addr,
                               const uint8_t *data, uint32_t len) {
    am_hal_mspi_pio_transfer_t xfer = {0};
    xfer.ui32NumBytes   = len;
    xfer.eDirection     = AM_HAL_MSPI_TX;
    xfer.bScrambling    = false;
    xfer.bSendInstr     = true;
    xfer.ui16DeviceInstr= instr;
    xfer.bSendAddr      = send_addr;
    xfer.ui32DeviceAddr = addr;
    xfer.pui32Buffer    = (uint32_t*)data;
    return am_hal_mspi_blocking_transfer(g_Mspi2Handle, &xfer, 10000);
}

static uint32_t mspi_pio_read(uint8_t instr, bool send_addr, uint32_t addr,
                              uint8_t *data, uint32_t len) {
    am_hal_mspi_pio_transfer_t xfer = {0};
    xfer.ui32NumBytes   = len;
    xfer.eDirection     = AM_HAL_MSPI_RX;
    xfer.bScrambling    = false;
    xfer.bSendInstr     = true;
    xfer.ui16DeviceInstr= instr;
    xfer.bSendAddr      = send_addr;
    xfer.ui32DeviceAddr = addr;
    xfer.pui32Buffer    = (uint32_t*)data;
    return am_hal_mspi_blocking_transfer(g_Mspi2Handle, &xfer, 10000);
}

// Change instruction/address byte lengths for next transactions (Ambiq HAL control)
static int set_addr_len_bytes(uint8_t nbytes) {
    am_hal_mspi_instr_addr_t cfg;
    cfg.eInstrCfg = AM_HAL_MSPI_INSTR_1_BYTE;
    switch (nbytes) {
        case 1: cfg.eAddrCfg = AM_HAL_MSPI_ADDR_1_BYTE; break;
        case 2: cfg.eAddrCfg = AM_HAL_MSPI_ADDR_2_BYTE; break;
        case 3: cfg.eAddrCfg = AM_HAL_MSPI_ADDR_3_BYTE; break;
        case 4: cfg.eAddrCfg = AM_HAL_MSPI_ADDR_4_BYTE; break;
        default: return -1;
    }
    uint32_t rc = am_hal_mspi_control(g_Mspi2Handle, AM_HAL_MSPI_REQ_SET_INSTR_ADDR_LEN, &cfg);
    return (rc == AM_HAL_STATUS_SUCCESS) ? 0 : -1;
}

static int get_features(uint8_t addr, uint8_t *val) {
    // Temporarily use 1-byte address for feature access
    if (set_addr_len_bytes(1) < 0) return -1;
    uint32_t rc = mspi_pio_read(NAND_CMD_GET_FEATURES, true, addr, val, 1);
    // Restore to default 3-byte addressing for row address commands
    set_addr_len_bytes(3);
    return (rc == AM_HAL_STATUS_SUCCESS) ? 0 : -1;
}

static int set_features(uint8_t addr, uint8_t val) {
    if (set_addr_len_bytes(1) < 0) return -1;
    uint32_t rc = mspi_pio_write(NAND_CMD_SET_FEATURES, true, addr, &val, 1);
    set_addr_len_bytes(3);
    return (rc == AM_HAL_STATUS_SUCCESS) ? 0 : -1;
}

int nand_write_enable(void) {
    uint32_t rc = mspi_pio_write(NAND_CMD_WRITE_ENABLE, false, 0, NULL, 0);
    return (rc == AM_HAL_STATUS_SUCCESS) ? 0 : -1;
}

int nand_reset(void) {
    uint32_t rc = mspi_pio_write(NAND_CMD_RESET, false, 0, NULL, 0);
    if (rc != AM_HAL_STATUS_SUCCESS) return -1;
    return nand_wait_ready(NAND_TIMEOUT_RESET_MS, NULL, NULL);
}

int nand_get_status(uint8_t *status) {
    if (!status) return -1;
    return get_features(NAND_FEATURE_ADDR_STATUS, status);
}

int nand_set_ecc_enabled(bool enable) {
    uint8_t v = 0;
    if (get_features(NAND_FEATURE_ADDR_ECC, &v) < 0) return -1;
    if (enable) v |= NAND_FEATURE_ECC_EN_MASK; else v &= (uint8_t)~NAND_FEATURE_ECC_EN_MASK;
    if (set_features(NAND_FEATURE_ADDR_ECC, v) < 0) return -1;
    // Optionally verify
    uint8_t vr = 0;
    if (get_features(NAND_FEATURE_ADDR_ECC, &vr) < 0) return -1;
    bool en_rd = (vr & NAND_FEATURE_ECC_EN_MASK) != 0;
    return (en_rd == enable) ? 0 : -2;
}

int nand_get_ecc_enabled(bool *enabled) {
    if (!enabled) return -1;
    uint8_t v = 0;
    if (get_features(NAND_FEATURE_ADDR_ECC, &v) < 0) return -1;
    *enabled = (v & NAND_FEATURE_ECC_EN_MASK) != 0;
    return 0;
}


int nand_wait_ready(uint32_t timeout_ms, nand_ecc_status_t *ecc, uint8_t *status_out) {
    uint32_t t_start = am_hal_stimer_counter_get();
    uint8_t st = 0;
    while (1) {
        if (nand_get_status(&st) < 0) return -1;
        if ((st & NAND_STATUS_OIP_MASK) == 0) break;
        // ~1ms spin (HFRC/6MHz: ~6000 ticks)
        am_util_delay_us(500);
        if (timeout_ms) {
            uint32_t t_now = am_hal_stimer_counter_get();
            uint32_t dt_ticks = (t_now >= t_start) ? (t_now - t_start) : (0xFFFFFFFF - t_start + t_now + 1);
            if ((dt_ticks / 6000) > timeout_ms) return -2; // timeout
        }
    }
    if (status_out) *status_out = st;
    if (ecc) {
        nand_ecc_status_t e = NAND_ECC_OK;
        uint8_t es = st & NAND_STATUS_ECC_MASK;
        if (es == NAND_STATUS_ECC_UNC) e = NAND_ECC_UNCORRECTABLE;
        else if (es == NAND_STATUS_ECC_CORR) e = NAND_ECC_CORRECTED;
        *ecc = e;
    }
    return 0;
}

int nand_read_id(uint8_t *mid, uint8_t *did) {
    uint8_t id[3] = {0};
    // Many parts return Manufacturer, MemoryType, Capacity
    if (mspi_pio_read(NAND_CMD_READ_ID, false, 0, id, sizeof(id)) != AM_HAL_STATUS_SUCCESS) {
        return -1;
    }
    if (mid) *mid = id[0];
    if (did) *did = id[1];
    return 0;
}

int nand_init(void) {
    if (!g_Mspi2Handle) {
        if (mspi2_init_spi_mode() != AM_HAL_STATUS_SUCCESS) {
            am_util_stdio_printf("MSPI2 init failed\r\n");
            return -1;
        }
    }
    if (nand_reset() < 0) return -1;
    // Optionally enable on-die ECC and Quad if desired (device-specific feature registers)
    // Left as configurable; defaults rely on part's power-on defaults.
    return 0;
}

static inline uint32_t rowaddr_from_page(uint32_t page_row) {
    // For SPI-NAND, 24-bit row address is sent as 3 bytes in many commands
    return page_row & 0x00FFFFFFu;
}

int nand_page_read_to_cache(uint32_t page_row) {
    // 3-byte row address
    if (set_addr_len_bytes(3) < 0) return -1;
    uint32_t rc = mspi_pio_write(NAND_CMD_PAGE_READ_TO_CACHE, true, rowaddr_from_page(page_row), NULL, 0);
    if (rc != AM_HAL_STATUS_SUCCESS) return -1;
    return nand_wait_ready(NAND_TIMEOUT_READ_MS, NULL, NULL);
}

int nand_read_from_cache(uint16_t col, uint8_t *buf, uint32_t len, nand_ecc_status_t *ecc) {
    if (!buf || (col + len) > (NAND_PAGE_SIZE + NAND_OOB_SIZE)) return -1;
    if (set_addr_len_bytes(2) < 0) return -1;
    uint32_t rc = mspi_pio_read(NAND_CMD_READ_FROM_CACHE, true, (uint32_t)col, buf, len);
    if (rc != AM_HAL_STATUS_SUCCESS) return -1;
    // Reading cache itself is immediate; ECC status applies to the preceding PAGE_READ_TO_CACHE
    // Query status to determine ECC result
    uint8_t st = 0;
    nand_ecc_status_t e = NAND_ECC_OK;
    if (nand_get_status(&st) == 0) {
        uint8_t es = st & NAND_STATUS_ECC_MASK;
        if (es == NAND_STATUS_ECC_UNC) e = NAND_ECC_UNCORRECTABLE;
        else if (es == NAND_STATUS_ECC_CORR) e = NAND_ECC_CORRECTED;
    }
    if (ecc) *ecc = e;
    return 0;
}

int nand_prog_load(uint16_t col, const uint8_t *buf, uint32_t len) {
    if (!buf || (col + len) > (NAND_PAGE_SIZE + NAND_OOB_SIZE)) return -1;
    if (nand_write_enable() < 0) return -1;
    if (set_addr_len_bytes(2) < 0) return -1;
    uint32_t rc = mspi_pio_write(NAND_CMD_PROG_LOAD, true, (uint32_t)col, buf, len);
    return (rc == AM_HAL_STATUS_SUCCESS) ? 0 : -1;
}

int nand_prog_exec(uint32_t page_row) {
    if (set_addr_len_bytes(3) < 0) return -1;
    uint32_t rc = mspi_pio_write(NAND_CMD_PROG_EXEC, true, rowaddr_from_page(page_row), NULL, 0);
    if (rc != AM_HAL_STATUS_SUCCESS) return -1;
    nand_ecc_status_t ecc = NAND_ECC_OK; uint8_t st = 0;
    if (nand_wait_ready(NAND_TIMEOUT_PROGRAM_MS, &ecc, &st) < 0) return -1;
    if (st & NAND_STATUS_P_FAIL_MASK) return -2;
    return 0;
}

int nand_block_erase(uint32_t block_idx) {
    uint32_t page0 = nand_block_to_page0(block_idx);
    if (nand_write_enable() < 0) return -1;
    if (set_addr_len_bytes(3) < 0) return -1;
    uint32_t rc = mspi_pio_write(NAND_CMD_BLOCK_ERASE, true, rowaddr_from_page(page0), NULL, 0);
    if (rc != AM_HAL_STATUS_SUCCESS) return -1;
    uint8_t st = 0;
    if (nand_wait_ready(NAND_TIMEOUT_ERASE_MS, NULL, &st) < 0) return -1;
    if (st & NAND_STATUS_E_FAIL_MASK) return -2;
    return 0;
}

int nand_is_subpage_free(uint32_t page_row, uint16_t subpage_col, uint32_t probe_len) {
    if ((subpage_col + probe_len) > NAND_PAGE_SIZE) return -1;
    uint8_t tmp[32];
    uint32_t remain = probe_len;
    uint16_t col = subpage_col;
    while (remain) {
        uint32_t chunk = (remain > sizeof(tmp)) ? sizeof(tmp) : remain;
        if (nand_page_read_to_cache(page_row) < 0) return -1;
        if (nand_read_from_cache(col, tmp, chunk, NULL) < 0) return -1;
        for (uint32_t i = 0; i < chunk; ++i) if (tmp[i] != 0xFF) return 0; // not free
        remain -= chunk; col += chunk;
    }
    return 1; // free
}

int nand_is_bad(uint32_t block_idx) {
    // Check spare[0] in first two pages
    for (uint32_t p = 0; p < NAND_BBM_CHECK_PAGES; ++p) {
        uint32_t page = nand_page_of(block_idx, p);
        if (nand_page_read_to_cache(page) < 0) return -1;
        uint8_t marker = 0xFF; nand_ecc_status_t ecc;
        if (nand_read_from_cache(NAND_OOB_COLUMN + NAND_BBM_COLUMN, &marker, 1, &ecc) < 0) return -1;
        if (marker != 0xFF) return 1; // bad
    }
    return 0; // good
}

int nand_mark_bad(uint32_t block_idx) {
    // Program 0x00 into spare[0] of page 0
    uint8_t bb = 0x00;
    uint32_t page = nand_page_of(block_idx, 0);
    if (nand_prog_load(NAND_OOB_COLUMN + NAND_BBM_COLUMN, &bb, 1) < 0) return -1;
    return nand_prog_exec(page);
}

