// Configurable storage/NAND geometry and SPI-NAND command/ECC policy
// Adjust these defaults to match the actual device. Values chosen for a typical 2Gbit SPI-NAND.
#ifndef STORAGE_CONFIG_H
#define STORAGE_CONFIG_H

// -------------------------
// NAND geometry (typical 2Gbit)
// -------------------------
#ifndef NAND_PAGE_SIZE
#define NAND_PAGE_SIZE            2048    // 2 KiB main area per page
#endif
#ifndef NAND_OOB_SIZE
#define NAND_OOB_SIZE             64      // spare/OOB bytes per page (many parts use 64B)
#endif
#ifndef NAND_PAGES_PER_BLOCK
#define NAND_PAGES_PER_BLOCK      64      // 64 pages per erase block is common
#endif
#ifndef NAND_BLOCKS
#define NAND_BLOCKS               2048    // 256MiB / (64*2KiB) = 2048 blocks
#endif

// -------------------------
// Dhara page size policy
// -------------------------
// Use 512-byte ECC-correctable logical pages for Dhara (commonly the on-die ECC granularity)
#ifndef DHARA_LOG2_PAGE_SIZE
#define DHARA_LOG2_PAGE_SIZE      9       // 2^9 = 512 bytes
#endif

// -------------------------
// SPI-NAND command set (JEDEC-like, common across vendors)
// -------------------------
#ifndef NAND_CMD_RESET
#define NAND_CMD_RESET            0xFF
#endif
#ifndef NAND_CMD_WRITE_ENABLE
#define NAND_CMD_WRITE_ENABLE     0x06
#endif
#ifndef NAND_CMD_GET_FEATURES
#define NAND_CMD_GET_FEATURES     0x0F
#endif
#ifndef NAND_CMD_SET_FEATURES
#define NAND_CMD_SET_FEATURES     0x1F
#endif
#ifndef NAND_CMD_READ_ID
#define NAND_CMD_READ_ID          0x9F
#endif
#ifndef NAND_CMD_PAGE_READ_TO_CACHE
#define NAND_CMD_PAGE_READ_TO_CACHE 0x13
#endif
#ifndef NAND_CMD_READ_FROM_CACHE
#define NAND_CMD_READ_FROM_CACHE  0x03    // slow read; 0x0B is fast read (requires dummy)
#endif
#ifndef NAND_CMD_PROG_LOAD
#define NAND_CMD_PROG_LOAD        0x02
#endif
#ifndef NAND_CMD_PROG_EXEC
#define NAND_CMD_PROG_EXEC        0x10
#endif
#ifndef NAND_CMD_BLOCK_ERASE
#define NAND_CMD_BLOCK_ERASE      0xD8
#endif

// Feature/Status register addresses (Winbond/Micron-style)
#ifndef NAND_FEATURE_ADDR_STATUS
#define NAND_FEATURE_ADDR_STATUS  0xC0
#endif
#ifndef NAND_FEATURE_ADDR_ECC
#define NAND_FEATURE_ADDR_ECC   0xB0
#endif
#ifndef NAND_FEATURE_ECC_EN_MASK
#define NAND_FEATURE_ECC_EN_MASK 0x01
#endif

// Status bits (make these match your part's datasheet)
#ifndef NAND_STATUS_OIP_MASK
#define NAND_STATUS_OIP_MASK      0x01    // Operation-In-Progress bit
#endif
#ifndef NAND_STATUS_P_FAIL_MASK
#define NAND_STATUS_P_FAIL_MASK   0x08    // Program fail bit (optional)
#endif
#ifndef NAND_STATUS_E_FAIL_MASK
#define NAND_STATUS_E_FAIL_MASK   0x04    // Erase fail bit (optional)
#endif
#ifndef NAND_STATUS_ECC_MASK
#define NAND_STATUS_ECC_MASK      0x30    // ECC status field mask (bits 4:5 typical)
#endif
#ifndef NAND_STATUS_ECC_UNC
#define NAND_STATUS_ECC_UNC       0x20    // Value indicating uncorrectable ECC (adjust per part)
#endif
#ifndef NAND_STATUS_ECC_CORR
#define NAND_STATUS_ECC_CORR      0x10    // Value indicating corrected ECC (adjust per part)
#endif

// -------------------------
// Timeouts (ms)
// -------------------------
#ifndef NAND_TIMEOUT_RESET_MS
#define NAND_TIMEOUT_RESET_MS     2
#endif
#ifndef NAND_TIMEOUT_READ_MS
#define NAND_TIMEOUT_READ_MS      5
#endif
#ifndef NAND_TIMEOUT_PROGRAM_MS
#define NAND_TIMEOUT_PROGRAM_MS   5
#endif
#ifndef NAND_TIMEOUT_ERASE_MS
#define NAND_TIMEOUT_ERASE_MS     400
#endif

// -------------------------
// Derived helpers
// -------------------------
#define NAND_BLOCK_SIZE           (NAND_PAGES_PER_BLOCK * NAND_PAGE_SIZE)
#define NAND_TOTAL_SIZE_BYTES     ((uint32_t)NAND_BLOCKS * (uint32_t)NAND_BLOCK_SIZE)
#define NAND_SUBPAGES_PER_PAGE    (NAND_PAGE_SIZE >> DHARA_LOG2_PAGE_SIZE)
#ifndef NAND_BBM_COLUMN
#define NAND_BBM_COLUMN          0
#endif


// OOB start column offset
#ifndef NAND_OOB_COLUMN
#define NAND_OOB_COLUMN           (NAND_PAGE_SIZE)
#endif

// Bad-block marker policy: check spare[0] of first (and/or second) page in block
#ifndef NAND_BBM_CHECK_PAGES
#define NAND_BBM_CHECK_PAGES      2       // check page 0 and page 1
#endif

#endif // STORAGE_CONFIG_H

