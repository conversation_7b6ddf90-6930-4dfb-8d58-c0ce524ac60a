// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the use
//   or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------

#ifndef FLASH_H
#define FLASH_H

#include <stddef.h>
#include <stdint.h>

#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"
#include "am_hal_global.h"
#include "am_hal_gpio.h"
#include "storage_config.h"

extern void *g_Mspi2Handle;

uint32_t mspi2_init_spi_mode(void);
uint32_t mspi2_write(uint8_t instr, uint32_t addr, uint8_t *data, uint32_t length);
uint32_t mspi2_read(uint8_t instr, uint32_t addr, uint8_t *data, uint32_t length);
int flash_test_program_page(uint32_t page_addr_row, uint16_t column, const uint8_t *buf, size_t len);
int flash_read_page(uint32_t page_addr_row, uint16_t column, uint8_t *out, size_t len);
int flash_set_addr_len_bytes(uint8_t nbytes);
int flash_get_feature(uint8_t addr, uint8_t *val);
int flash_set_feature(uint8_t addr, uint8_t val);
int flash_wait_ready_us(uint32_t timeout_us, uint8_t *status_out);
uint8_t gpio_flash_test(void);

#endif // FLASH_H
