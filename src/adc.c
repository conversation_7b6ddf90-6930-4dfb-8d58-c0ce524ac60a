// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the use
//   or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------


#include "adc.h"


//*****************************************************************************
//
// AUDADC Sample buffer.
//
//*****************************************************************************


AM_SHARED_RW uint32_t g_ui32AUDADCSampleBuffer[AUDADC_SAMPLE_BUF_SIZE + AUDADC_SAMPLE_BUF_SIZE + 3];

AM_SHARED_RW int16_t g_in16AudioDataBuffer[AUDADC_DATA_BUFFER_SIZE];

am_hal_audadc_sample_t sLGSampleBuffer[AUDADC_SAMPLE_BUF_SIZE];
am_hal_audadc_sample_t sHGSampleBuffer[AUDADC_SAMPLE_BUF_SIZE];
//
// RTT streaming buffer
//
#if AUDADC_EXAMPLE_DEBUG
#define TIMEOUT                     400000      // RTT streaming timeout loop count
#define RTT_BUFFER_LENGTH           (256*1024)
#define AUDIO_SAMPLE_TO_RTT         (256*1024)
uint8_t g_rttRecorderBuffer[RTT_BUFFER_LENGTH];
AM_SHARED_RW int16_t g_in16SampleToRTT[AUDIO_SAMPLE_TO_RTT];
uint32_t g_ui32SampleToRTT = 0;
#endif

//
// AUDADC Device Handle.
//
void *g_AUDADCHandle;

//
// AUDADC DMA complete flag.
//
volatile bool g_bAUDADCDMAComplete;

//
// AUDADC DMA error flag.
//
volatile bool g_bAUDADCDMAError;

// AXI Scratch buffer
// On Apollo4B, need to allocate 20 Words even though we only need 16, to ensure we have 16 Byte alignment
#ifdef AM_PART_APOLLO4B
AM_SHARED_RW uint32_t axiScratchBuf[20];
#endif
//*****************************************************************************
//
// AUDADC gain configuration information.
//
//*****************************************************************************


am_hal_audadc_gain_config_t g_sAudadcGainConfig =
{
    .ui32LGA        = 0,        // 0 code
    .ui32HGADELTA   = 0,        // delta from the LGA field
    .ui32LGB        = 0,        // 0 code
    .ui32HGBDELTA   = 0,        // delta from the LGB field

    .eUpdateMode    = AM_HAL_AUDADC_GAIN_UPDATE_IMME,
};

//
// Configure the AUDADC to use DMA for the sample transfer.
//
am_hal_audadc_dma_config_t g_sAUDADCDMAConfig =
{
    .bDynamicPriority  = true,
    .ePriority         = AM_HAL_AUDADC_PRIOR_SERVICE_IMMED,
    .bDMAEnable        = true,
    .ui32SampleCount   = AUDADC_SAMPLE_BUF_SIZE,
    .ui32TargetAddress = 0x0,
    .ui32TargetAddressReverse = 0x0,
};

//
// helper function.
//
#if AUDADC_EXAMPLE_DEBUG
void pcm_rtt_record_slow(void* pBuffer, uint32_t NumBytes)
{
    uint32_t timeout = TIMEOUT;
    uint32_t bytesEveryTime = 512;
    uint32_t bytes_stored;
    int32_t u32Bytes = NumBytes;
    void* data = pBuffer;

    for (; u32Bytes >= 0; u32Bytes -= bytesEveryTime )
    {
        while (timeout--);
        timeout = TIMEOUT;

        //bytes_stored = SEGGER_RTT_Write(1, data, bytesEveryTime);
        while ((bytes_stored != bytesEveryTime));

        data = (void*) ((uint32_t)data + bytesEveryTime);
    }
}
#endif

//*****************************************************************************
//
// Set up the core for sleeping, and then go to sleep.
//
//*****************************************************************************
void
sleep(void)
{
    //
    // Go to Deep Sleep.
    //
    am_hal_sysctrl_sleep(AM_HAL_SYSCTRL_SLEEP_DEEP);

}

//*****************************************************************************
//
// Configure the AUDADC SLOT.
//
//*****************************************************************************
void
audadc_slot_config(void)
{
    am_hal_audadc_slot_config_t      AUDADCSlotConfig;

    //
    // Set up an AUDADC slot
    //
    AUDADCSlotConfig.eMeasToAvg      = AM_HAL_AUDADC_SLOT_AVG_1;
    AUDADCSlotConfig.ePrecisionMode  = AM_HAL_AUDADC_SLOT_12BIT;
#if (CLK_SRC == XTHS)
    AUDADCSlotConfig.ui32TrkCyc      = 24;
#else
    AUDADCSlotConfig.ui32TrkCyc      = 34;
#endif
    AUDADCSlotConfig.eChannel        = AM_HAL_AUDADC_SLOT_CHSEL_SE0;
    AUDADCSlotConfig.bWindowCompare  = false;
    AUDADCSlotConfig.bEnabled        = true;

#if CH_A0_EN
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_configure_slot(g_AUDADCHandle, 0, &AUDADCSlotConfig))
    {
        am_util_stdio_printf("Error - configuring AUDADC Slot 0 failed.\n");
    }
#endif
#if CH_A1_EN
    AUDADCSlotConfig.eChannel        = AM_HAL_AUDADC_SLOT_CHSEL_SE1;
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_configure_slot(g_AUDADCHandle, 1, &AUDADCSlotConfig))
    {
        am_util_stdio_printf("Error - configuring AUDADC Slot 1 failed.\n");
    }
#endif
#if CH_B0_EN
    AUDADCSlotConfig.eChannel        = AM_HAL_AUDADC_SLOT_CHSEL_SE2;
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_configure_slot(g_AUDADCHandle, 2, &AUDADCSlotConfig))
    {
        am_util_stdio_printf("Error - configuring AUDADC Slot 2 failed.\n");
    }
#endif
#if CH_B1_EN
    AUDADCSlotConfig.eChannel        = AM_HAL_AUDADC_SLOT_CHSEL_SE3;
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_configure_slot(g_AUDADCHandle, 3, &AUDADCSlotConfig))
    {
        am_util_stdio_printf("Error - configuring AUDADC Slot 3 failed.\n");
    }
#endif
}

//*****************************************************************************
//
// Configure the AUDADC.
//
//*****************************************************************************
void
audadc_config(void)
{
    //
    // Set up the AUDADC configuration parameters. These settings are reasonable
    // for accurate measurements at a low sample rate.
    //
    am_hal_audadc_config_t           AUDADCConfig =
    {
#if (CLK_SRC == XTHS)
        .eClock             = AM_HAL_AUDADC_CLKSEL_XTHS_24MHz,
#elif (CLK_SRC == HFRC)
        .eClock             = AM_HAL_AUDADC_CLKSEL_HFRC_48MHz,
#elif (CLK_SRC == HFRC2) || (CLK_SRC == HFRC2_ADJ)
        .eClock             = AM_HAL_AUDADC_CLKSEL_HFRC2_48MHz,
#else
        #warning Invalid clock source.
#endif
        .ePolarity          = AM_HAL_AUDADC_TRIGPOL_RISING,
        .eTrigger           = AM_HAL_AUDADC_TRIGSEL_SOFTWARE,
        .eClockMode         = AM_HAL_AUDADC_CLKMODE_LOW_LATENCY,
        .ePowerMode         = AM_HAL_AUDADC_LPMODE0,
        .eRepeat            = AM_HAL_AUDADC_REPEATING_SCAN,
        .eRepeatTrigger     = AM_HAL_AUDADC_RPTTRIGSEL_INT,
        .eSampMode          = AM_HAL_AUDADC_SAMPMODE_MED,       //AM_HAL_AUDADC_SAMPMODE_LP,
    };

    //
    // Set up internal repeat trigger timer
    //
    am_hal_audadc_irtt_config_t      AUDADCIrttConfig =
    {
        .bIrttEnable        = true,
        .eClkDiv            = AM_HAL_AUDADC_RPTT_CLK_DIV32,
        //
        // Adjust sample rate to around 16K.
        // sample rate = eClock/eClkDiv/(ui32IrttCountMax+1)
        //
#if (CLK_SRC == XTHS) // On Apollo4P_EB, XTHS is 32 MHZ.
        .ui32IrttCountMax   = 62,
#elif (CLK_SRC == HFRC)
        .ui32IrttCountMax   = 93,
#elif (CLK_SRC == HFRC2)
        .ui32IrttCountMax   = 93,
#elif (CLK_SRC == HFRC2_ADJ)
        .ui32IrttCountMax   = 93,
#else
        #warning Invalid clock source.
#endif
    };

    //
    // Initialize the AUDADC and get the handle.
    //
    if ( AM_HAL_STATUS_SUCCESS != am_hal_audadc_initialize(0, &g_AUDADCHandle) )
    {
        am_util_stdio_printf("Error - reservation of the AUDADC instance failed.\n");
    }
    //
    // Power on the AUDADC.
    //
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_power_control(g_AUDADCHandle,
                                                          AM_HAL_SYSCTRL_WAKE,
                                                          false) )
    {
        am_util_stdio_printf("Error - AUDADC power on failed.\n");
    }
#if defined(AM_PART_APOLLO4) || defined(AM_PART_APOLLO4B)
    #if (CLK_SRC == HFRC2) || (CLK_SRC == HFRC2_ADJ)
        //
        //Enable hfrc2.
        //
        am_hal_clkgen_control(AM_HAL_CLKGEN_CONTROL_HFRC2_START, false);
        am_util_delay_us(200);
    #endif
#endif
    am_hal_mcuctrl_control_arg_t ctrlArgs = g_amHalMcuctrlArgDefault;
    ctrlArgs.ui32_arg_hfxtal_user_mask   = 1 << AM_HAL_HFXTAL_AUADC_EN;
#if (CLK_SRC == HFRC2_ADJ)
        //
        // hfrc2 adj.
        //
        am_hal_mcuctrl_control(AM_HAL_MCUCTRL_CONTROL_EXTCLK32M_KICK_START, (void *) &ctrlArgs);
        am_util_delay_us(1500);
        //
        // set HF2ADJ for 49.152MHz output
        //
        am_hal_clkgen_control(AM_HAL_CLKGEN_CONTROL_HF2ADJ_ENABLE, false);

        am_util_delay_us(500);      // wait for adj to apply
#endif

    if ( AUDADCConfig.eClock == AM_HAL_AUDADC_CLKSEL_XTHS_24MHz )
    {
        am_hal_mcuctrl_control(AM_HAL_MCUCTRL_CONTROL_EXTCLK32M_NORMAL, (void *) &ctrlArgs);
        am_util_delay_us(1500);
    }

    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_configure(g_AUDADCHandle, &AUDADCConfig))
    {
        am_util_stdio_printf("Error - configuring AUDADC failed.\n");
    }

    //
    // Set up internal repeat trigger timer
    //
    am_hal_audadc_configure_irtt(g_AUDADCHandle, &AUDADCIrttConfig);

    //
    // Enable the AUDADC.
    //
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_enable(g_AUDADCHandle))
    {
        am_util_stdio_printf("Error - enabling AUDADC failed.\n");
    }

    //
    // Enable internal repeat trigger timer
    //
    am_hal_audadc_irtt_enable(g_AUDADCHandle);

    //
    // Configure the AUDADC to use DMA for the sample transfer.
    //
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_configure_dma(g_AUDADCHandle, &g_sAUDADCDMAConfig))
    {
        am_util_stdio_printf("Error - configuring AUDADC DMA failed.\n");
    }

    //
    // For this example, the samples will be coming in slowly. This means we
    // can afford to wake up for every conversion.
    //
    am_hal_audadc_interrupt_enable(g_AUDADCHandle, AM_HAL_AUDADC_INT_FIFOOVR1 | AM_HAL_AUDADC_INT_FIFOOVR2 | AM_HAL_AUDADC_INT_DERR | AM_HAL_AUDADC_INT_DCMP ); //| AM_HAL_AUDADC_INT_CNVCMP | AM_HAL_AUDADC_INT_SCNCMP);
}

//*****************************************************************************
//
// Interrupt handler for the AUDADC.
//
//*****************************************************************************
void
am_audadc0_isr(void)
{
    uint32_t ui32IntMask;
    //
    // Read the interrupt status.
    //
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_interrupt_status(g_AUDADCHandle, &ui32IntMask, false))
    {
        am_util_stdio_printf("Error reading AUDADC interrupt status\n");
    }

    //
    // Clear the AUDADC interrupt.
    //
    if (AM_HAL_STATUS_SUCCESS != am_hal_audadc_interrupt_clear(g_AUDADCHandle, ui32IntMask))
    {
        am_util_stdio_printf("Error clearing AUDADC interrupt status\n");
    }

    //
    // If we got a DMA complete, set the flag.
    //
    if (ui32IntMask & AM_HAL_AUDADC_INT_FIFOOVR1)
    {
        if ( AUDADCn(0)->DMASTAT_b.DMACPL )
        {
            g_bAUDADCDMAError = false;
            am_hal_audadc_interrupt_service(g_AUDADCHandle, &g_sAUDADCDMAConfig);

            g_bAUDADCDMAComplete = true;
        }
    }

    //
    // If we got a DMA error, set the flag.
    //
    if ( ui32IntMask & AM_HAL_AUDADC_INT_DERR )
    {
        g_bAUDADCDMAError = true;
    }
}