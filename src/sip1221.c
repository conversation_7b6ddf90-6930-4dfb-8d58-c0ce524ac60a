#include "sip1221.h"
#include "si_sensor.h"
#include <string.h>
#include <stdbool.h>

struct sip1221_device g_sensor_device = {
    .ainfo = {
        .conf = {
            .is_dri = false,
            .long_inte_time = 100.0},
        .cali = {
            //classify
            .ch0_coef_c = 1054.009,
            .ch1_coef_c = 0,
            .ch0_coef_ad = 1054.009,
            .ch1_coef_ad = 0,
            .ch0_coef_h = 863.136,
            .ch1_coef_h = 0,
            .ratio_spec_1 = 1.0,
            .ratio_spec_2 = 3.1,
            .light_ch0_coef = 1.0,
            .light_ch1_coef = 1.0,  
            //lux offset
            .offset_a_c = 0,
            .offset_b_c = 0,
            .offset_c_c = 0,
            .offset_a_ad = 0,
            .offset_b_ad = 0,
            .offset_c_ad = 0,
            .offset_a_h = 0,
            .offset_b_h = 0,
            .offset_c_h = 0,
        },
    },
    .iinfo = {
        .write_reg = sip_write_reg,
        .write_regs = sip_write_regs,
        .read_reg = sip_read_reg,
        .read_regs = sip_read_regs,
        .modify_reg = sip_modify_reg,
    },
};

static als_gain_map als_gains[] = {
    {0.25, 0.25},
    {0.5, 0.5},
    {1, 1},
    {2, 2},
    {4, 4},
    {8, 8},
    {16, 16},
    {32, 32},
    {64, 64},
    {128, 128},
    {256, 256},
    {512, 512},
    {1024, 1024},
};

sip_reg_t esd_regs[] = {
    {SIP1221_CLK_CTRL, 0x33},
    {SIP1221_CTRL, 0x03},
};

#if SIP1221_SUPPORT_DUMP
int sip1221_dump_reg(sip_i2c_info_t *i2c_info)
{
    uint8_t value;
    sip_reg_t sip1221_dump_regs[] =
        {
            {SIP1221_CLK_CTRL, 0x00},
            {SIP1221_CTRL, 0x00},
            {SIP1221_CTRL, 0x00},
            {SIP1221_INT_CTRL, 0x00},
            {SIP1221_ALS_CTRL0, 0x00},
            {SIP1221_ALS_CTRL1, 0x00},
            {SIP1221_ALS_INTEN, 0x00},
            {SIP1221_ALS_AZ_CTRL, 0x00},
            {SIP1221_ALS_THLOW_H, 0x00},
            {SIP1221_ALS_THLOW_L, 0x00},
            {SIP1221_ALS_THHIGH_H, 0x00},
            {SIP1221_ALS_THHIGH_L, 0x00},
            {SIP1221_ALS_INTE_TIME_H, 0x00},
            {SIP1221_ALS_INTE_TIME_M, 0x00},
            {SIP1221_ALS_INTE_TIME_L, 0x00},
            {SIP1221_ALS0_1_GAIN, 0x00},
            {SIP1221_ALS_AZ_EN, 0x00},
            {SIP1221_RESERVE1, 0x00},
            {SIP1221_ALS0_1_GAIN, 0x00},
            {0x6F, 0x56},
            {0x70, 0x35},
        };

    for (uint8_t i = 0; i < ARR_SIZE(sip1221_dump_regs); ++i)
    {
        SIP_ASSERT(i2c_info->read_reg(sip1221_dump_regs[i].reg, &value));
        logi("sip1221 dump reg[0x%02x] = 0x%02x\r", sip1221_dump_regs[i].reg, value);
    }
    return 0;
}
#endif

static struct sip1221_device *get_sip1221_device()
{
    return &g_sensor_device;
}

int sip1221_esd_check_conf(sip_i2c_info_t *i2c_info)
{
    uint8_t value = 0x00;

    for (uint8_t i = 0; i < ARR_SIZE(esd_regs); ++i)
    {
        if (0 == esd_regs[i].reg)
        {
            continue;
        }

        i2c_info->read_reg(esd_regs[i].reg, &value);
        if (value != esd_regs[i].value)
        {
            logi("read 0x%02X: 0x%02X", esd_regs[i].reg, value);
            return -1;
        }
    }

    return 0;
}

/*****ALS Part start*******/
int find_highest_bit(uint32_t value)
{
    int8_t i;
    if (value == 0)
        return 0;
    for (i = INTEGER_SIZE - 1; i >= 0; i--)
        if ((value >> i) & 1)
            return i;
    return 0;
}

int sip1221_optimize_gain(uint32_t maximum_adc, uint32_t max,
                          uint8_t *gain_index, uint8_t *saturation)
{
    uint32_t gain_change = 0;
    if (max == 0)
        max = 1;
    if (max >= maximum_adc)
    {
        if (*gain_index > LOW_AUTO_GAIN_VALUE)
        { // Lower_Auto_gain_value is 3
            *gain_index /= AUTO_GAIN_DIV;
        }
        else
        {
            *gain_index = LOWEST_GAIN_ID;
        }
        *saturation = 1;
    }
    else
    {
        gain_change = (maximum_adc * SATURATION_LOW_PERCENT) /
                      (max * SATURATION_HIGH_PERCENT);
        if (gain_change == 0 && (*gain_index) != 0)
        {
            (*gain_index)--;
        }
        else
        {
            gain_change = find_highest_bit(gain_change);
            if (((*gain_index) + gain_change) > MAX_GAIN_ID)
            {
                *gain_index = MAX_GAIN_ID;
            }
            else
            {
                *gain_index += gain_change;
            }
        }
        *saturation = 0;
    }
    logi(
        "sip1221_optimize_gain function gain_index is = %d, gain_change is %d, "
        "saturation status is %d",
        *gain_index, gain_change, *saturation);
    return 1;
}

int sip1221_set_als_channel_gain(sip_i2c_info_t *i2c_info, uint16_t channel, float again)
{
    int ret = 0;
    uint8_t count = 0, al_gain = 0;
    uint8_t rx_buf[1] = {0};
    uint8_t sx_buf[1] = {0};
    uint8_t tx_buf[3] = {0};
    uint16_t tmp_gain = (uint16_t)(again * 4);

    while ((tmp_gain / 2) != 0)
    {
        count++;
        tmp_gain /= 2;
    }

    tx_buf[0] = SIP1221_ALS_ENABLE;
    SIP_ASSERT(i2c_info->read_reg(tx_buf[0], &rx_buf[0]));

    SIP_ASSERT(i2c_info->modify_reg(SIP1221_ALS_ENABLE,0x00,ALS_EN_ALL));
    tx_buf[0] = SIP1221_ALS0_1_GAIN;
    SIP_ASSERT(i2c_info->read_reg(tx_buf[0], &sx_buf[0]));

    if (channel)
    {
        al_gain = (count << 4) & (G_GAIN_MASK);
        sx_buf[0] &= W_GAIN_MASK;
    }
    else
    {
        al_gain = (count & W_GAIN_MASK);
        sx_buf[0] &= G_GAIN_MASK;
    }

    tx_buf[0] = SIP1221_ALS0_1_GAIN;
    tx_buf[1] = sx_buf[0] | al_gain;
    SIP_ASSERT(i2c_info->write_reg(tx_buf[0], tx_buf[1]));

    tx_buf[0] = SIP1221_ALS_ENABLE;
    tx_buf[1] = rx_buf[0];
    SIP_ASSERT(i2c_info->write_reg(tx_buf[0], tx_buf[1]));
    return ret;
}

static int sip1221_set_als_gain(sip_i2c_info_t *i2c_info, float again)
{
    int ret = 0;
    SIP_ASSERT(sip1221_set_als_channel_gain(i2c_info, 0, again));
    SIP_ASSERT(sip1221_set_als_channel_gain(i2c_info, 1, again));
    return ret;
}

int sip1221_get_int_status(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    SIP_ASSERT(i2c_info->read_reg(SIP1221_ALS_INT_STATUS, &als_info->stat.int_status));
    return 0;
}

static int sip1221_als_enable_private(sip_i2c_info_t *i2c_info, uint8_t bo)
{
    SIP_ASSERT(i2c_info->modify_reg(SIP1221_ALS_ENABLE, bo ? 0x1F : 0x00, 0x1F));
    return 0;
}

static int sip1221_func_als_enable(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info, uint8_t bo)
{
    if (als_info->stat.enabled == bo)
    {
        logi("repeatedly enable als: %d", bo);
        return -1;
    }

    als_info->stat.enabled = bo;

    SIP_ASSERT(sip1221_als_enable_private(i2c_info, bo));
    logi("status = %d", bo);

    return 0;
}

int sip1221_als_auto_gain(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    int ret = 0;
    uint32_t max = 0, saturation = 0;
    uint8_t als_gain_reg_val;
    uint8_t old_gain_val, saturation_status, a0gain_val = 0;

    saturation = als_info->tmp.max_range;
    SIP_ASSERT(i2c_info->read_reg(SIP1221_ALS0_1_GAIN, &als_gain_reg_val));
    a0gain_val = (als_gain_reg_val & W_GAIN_MASK);

    max = max(als_info->out.raw[0], als_info->out.raw[1]);
    old_gain_val = a0gain_val;
	
	als_info->tmp.gain0_count = a0gain_val;
    als_info->tmp.tgain0 = als_gains[a0gain_val].rgain;
	
    sip1221_optimize_gain(saturation, max, &a0gain_val, &saturation_status);

    if (old_gain_val != a0gain_val)
    {
        sip1221_set_als_gain(i2c_info, als_gains[a0gain_val].rgain);
    }



    logi(
        "sip1221_optimize_gain old_gain_index is = %d, new_gain_index "
        "is %d,  saturation value is %d",
        old_gain_val, a0gain_val, saturation);
    
    if (saturation_status)
        return -1;

    return 0;
}

static int sip1221_als_set_itime(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info, float inte_time)
{
    unsigned int i = 0;
    uint32_t al_inte_step_time = 0;
    uint8_t tx_buf[3] = {0};

    al_inte_step_time = (uint32_t)(inte_time * 1000 / 1.358f - 1);
    tx_buf[0] = (unsigned char)((al_inte_step_time >> 16) & 0x03);
    tx_buf[1] = (unsigned char)((al_inte_step_time >> 8) & 0xFF);
    tx_buf[2] = (unsigned char)(al_inte_step_time & 0xFF);

    SIP_ASSERT(i2c_info->write_regs(SIP1221_ALS_INTE_TIME_H, tx_buf, sizeof(tx_buf)));

    als_info->tmp.inte_time = inte_time;
    if (tx_buf[0] > 0)
    {
        als_info->tmp.max_range = MAX_ALS_VALUE;
    }
    else
    {
        als_info->tmp.max_range = min((((tx_buf[1] << 8) | tx_buf[2]) + 1), MAX_ALS_VALUE);
    }
    return 0;
}

static int sip1221_als_set_int_mode(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    uint8_t int_ctrl = 0x0;
    uint8_t als_int_en = 0x0;
    if (als_info->conf.is_dri)
    {
        int_ctrl = INT_EN;
        als_int_en = ALS_INT_EN;

        SIP_ASSERT(i2c_info->modify_reg(SIP1221_INT_CTRL, int_ctrl, int_ctrl));
        SIP_ASSERT(i2c_info->modify_reg(SIP1221_ALS_INTEN, als_int_en, als_int_en));
    }
    return 0;
}

int sip1221_soft_reset(sip_i2c_info_t *i2c_info)
{
    return i2c_info->write_reg(SIP1221_CTRL, 0x00);
}

int sip1221_als_checktrim(sip_i2c_info_t *i2c_info)
{
    int i = 0;
    int ret;
    int zero_cnt = 0;
    sip_reg_t trim_regs[] = {
        {0xd4, 0x00},
        {0xd5, 0x00},  
        {0xd6, 0x00}, 
        {0xd7, 0x00}, 
    };

    for (i = 0; i < sizeof(trim_regs) / sizeof(sip_reg_t); i++)
    {
        ret = i2c_info->read_reg(trim_regs[i].reg, &trim_regs[i].value);
        if (ret)
        {
            logi("write reg failed\n");
        }
        if (trim_regs[i].value == 0x00)
        {
            zero_cnt++;
        }
    }
		
    if (zero_cnt == 4) 
    {
        ret |= i2c_info->write_reg(0xB0,0x00);
        ret |= i2c_info->write_reg(0xB1,0x00);
        logi("trim is not set\n");
    }
    return ret;
}

int sip1221_als_func_init(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    int i = 0;
    sip_reg_t init_regs[] = {
        {SIP1221_CTRL, 0x03},
        {SIP1221_INT_CTRL, 0x00},
        {SIP1221_ALS_CTRL0, 0xA0},
        {SIP1221_ALS_CTRL1, 0x1C},
        {SIP1221_ALS_INTEN, 0x80},
        {SIP1221_ALS_AZ_CTRL, 0xFF},
        {SIP1221_ALS_THLOW_H, 0x00},
        {SIP1221_ALS_THLOW_L, 0x0A},
        {SIP1221_ALS_THHIGH_H, 0x00},
        {SIP1221_ALS_THHIGH_L, 0x1A},
        {SIP1221_ALS_AZ_EN, 0xC1},
        {SIP1221_RESERVE1, 0x05},
        {SIP1221_ALS0_1_GAIN, 0xCC},
        {0x6F, 0x56},
        {0x70, 0x35},
    };

    for (i = 0; i < ARR_SIZE(init_regs); ++i)
    {
        SIP_VERIFY2(i2c_info->write_reg(init_regs[i].reg, init_regs[i].value) >= 0,
                    "light sensor init-%d failed!", i);
    }

    sip1221_als_set_itime(i2c_info, als_info, als_info->conf.long_inte_time);
    sip1221_als_set_int_mode(i2c_info, als_info);
    return 0;
}

int sip1221_als_get_int_stat(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    return i2c_info->read_reg(SIP1221_ALS_INT_STATUS, &als_info->stat.int_status);
}

int sip1221_als_get_stat(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    int ret = 0;
    SIP_ASSERT(i2c_info->read_reg(SIP1221_DATA_VALID, &als_info->stat.data_valid));
    SIP_ASSERT(i2c_info->read_reg(SIP1221_ALS_ENABLE, &(als_info->stat.enable_status)));

    logi("data_valid: 0x%02X, int_status: 0x%02X, enable_status: 0x%02X",
         als_info->stat.data_valid, als_info->stat.int_status, als_info->stat.enable_status);
    return ret;
}

int sip1221_als_analyse_stat(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    int ret = 0;
    int esd_flag = 0;
    if ((als_info->stat.data_valid & ALS_DATA_VALID))
    {
        als_info->stat.data_invalid_times = 0;
    }
    else
    {
        als_info->stat.data_invalid_times++;
        ret = -1;
#if SIP1221_SUPPORT_DUMP
        sip1221_dump_reg(i2c_info);
#endif
    }

    if (sip1221_esd_check_conf(i2c_info))
    {
        esd_flag = 1;
    }

    if (als_info->stat.data_invalid_times > 10 || esd_flag)
    {
        logi("trigger esd check");
        als_info->stat.data_invalid_times = 0;
        sip1221_soft_reset(i2c_info);
        mdelay(1);
        sip1221_als_func_init(i2c_info, als_info);
        if (als_info->stat.enabled)
        {
            sip1221_als_enable_private(i2c_info, true);
        }
    }
    return ret;
}

static int sip1221_als_read_raw_data(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    uint8_t rx_buf[8] = {0};

    SIP_ASSERT(i2c_info->read_regs(SIP1221_ALS_DATA, rx_buf, sizeof(rx_buf)));
    als_info->out.raw[0] = (uint16_t)((uint16_t)rx_buf[4] << 8) | rx_buf[5]; // als
    als_info->out.raw[1] = (uint16_t)((uint16_t)rx_buf[6] << 8) | rx_buf[7]; // wb

    if (als_info->out.raw[0] == 0) 
    {
        als_info->out.raw[0] = 1;
    }
    logi("sip1221 als_data.als_ir = %d,als_data.photoic = %d", als_info->out.raw[1], als_info->out.raw[0]);
    return 0;
}

static int sip1221_als_compute_lux(sip_als_info_t *als_info)
{
    float ratio;
    uint16_t ch0_raw, ch1_raw;
    float lux, cpl;
    float lux_offset;
    uint16_t bk_level = als_info->tmp.brightness;
    cpl = (uint32_t)(als_info->tmp.inte_time * als_info->tmp.tgain0);

    //als ch0_raw
    //wb ch1_raw
    ch0_raw = als_info->out.raw[0];
    ch1_raw = als_info->out.raw[1];

    //wb / als
    ratio = ch1_raw * 1.0 / ch0_raw;

    if (ratio < als_info->cali.ratio_spec_1)
    {
        lux = (ch0_raw * als_info->cali.ch0_coef_c * als_info->cali.light_ch0_coef + ch1_raw * als_info->cali.ch1_coef_c *als_info->cali.light_ch1_coef) / cpl;
    }
    else if ((ratio > als_info->cali.ratio_spec_1 && ratio < als_info->cali.ratio_spec_2))
    {
        lux = (ch0_raw * als_info->cali.ch0_coef_ad * als_info->cali.light_ch0_coef + ch1_raw * als_info->cali.ch1_coef_ad * als_info->cali.light_ch1_coef) / cpl;
    }
    else
    {
        lux = (ch0_raw * als_info->cali.ch0_coef_h * als_info->cali.light_ch0_coef + ch1_raw * als_info->cali.ch1_coef_h * als_info->cali.light_ch1_coef) / cpl;
    }

    als_info->out.lux = lux;

    #if SIP1221_LUX_OFFSET
    if (bk_level > 44)
    {
        lux_offset = (0.002 * bk_level*bk_level - 0.053*bk_level + 1.793) / 512;

        if (ratio < als_info->cali.ratio_spec_1)
        {
            lux_offset = lux_offset * als_info->cali.ch0_coef_c;
        }
        else if ((ratio > als_info->cali.ratio_spec_1 && ratio < als_info->cali.ratio_spec_2))
        {
            lux_offset = lux_offset * als_info->cali.ch0_coef_ad;
        }
        else
        {
            lux_offset = lux_offset * als_info->cali.ch0_coef_h;
        }
    }
    
    if (lux_offset < 0)
        lux_offset = 0;

    als_info->out.lux = (lux - lux_offset);
    #endif
	if (als_info->out.lux < 0)
        als_info->out.lux = 10;

	logi("light_ch0_coef = %f,light_ch1_coef = %f", als_info->cali.light_ch0_coef,als_info->cali.light_ch1_coef);
    logi("lux = %f,cpl = %f,ratio = %f,lux_offset = %f,origin lux = %f,bk_level = %d", als_info->out.lux, cpl, ratio,lux_offset,lux,bk_level);

    return 0;
}

int sip1221_als_update_threhold(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    uint16_t threhold_high;
    uint16_t threhold_low;
    uint16_t als_raw = als_info->out.raw[0];

    if ((als_raw + HIGH_THREHOLD_OFFSET) > als_info->tmp.max_range)
    {
        threhold_high = als_raw;
    }
    else
    {
        threhold_high = als_raw + HIGH_THREHOLD_OFFSET;
    }

    if ((als_raw - LOW_THREHOLD_OFFSET) < 0)
    {
        threhold_low = als_raw;
    }
    else
    {
        threhold_low = als_raw - LOW_THREHOLD_OFFSET;
    }

    SIP_ASSERT(i2c_info->write_reg(SIP1221_ALS_THLOW_H, threhold_low >> 8));
    SIP_ASSERT(i2c_info->write_reg(SIP1221_ALS_THLOW_L, threhold_low & 0xff));
    SIP_ASSERT(i2c_info->write_reg(SIP1221_ALS_THHIGH_H, threhold_high >> 8));
    SIP_ASSERT(i2c_info->write_reg(SIP1221_ALS_THHIGH_L, threhold_high & 0xff));

    logi("threhold_high = %d,threhold_low = %d", threhold_high, threhold_low);
    return 0;
}

int sip1221_als_enable(void)
{
    struct sip1221_device *dev = get_sip1221_device();
    sip_i2c_info_t *i2c_info = &dev->iinfo;
    sip_als_info_t *als_info = &dev->ainfo;
    return sip1221_func_als_enable(i2c_info, als_info, 1);
}

int sip1221_als_factory_enable(void)
{
    struct sip1221_device *dev = get_sip1221_device();
    sip_i2c_info_t *i2c_info = &dev->iinfo;
    sip_als_info_t *als_info = &dev->ainfo;

    SIP_ASSERT(i2c_info->write_reg(SIP1221_ALS0_1_GAIN, GAIN_256x));
    return sip1221_func_als_enable(i2c_info, als_info, 1);
}

int sip1221_als_disable(void)
{
    int ret = 0;
    struct sip1221_device *dev = get_sip1221_device();
    sip_i2c_info_t *i2c_info = &dev->iinfo;
    sip_als_info_t *als_info = &dev->ainfo;
    ret = sip1221_func_als_enable(i2c_info, als_info, 0);

    return ret;
}

int sip1221_als_factory_disable(void)
{
    return sip1221_als_disable();
}


int sip1221_als_sample(float *lux)
{
    uint8_t saturation_status = 0;
    struct sip1221_device *dev = get_sip1221_device();
    sip_i2c_info_t *i2c_info = &dev->iinfo;
    sip_als_info_t *als_info = &dev->ainfo;

    SIP_ASSERT(sip1221_als_get_int_stat(i2c_info, als_info));
    SIP_ASSERT(sip1221_als_get_stat(i2c_info, als_info));
    SIP_ASSERT(sip1221_als_analyse_stat(i2c_info, als_info));
    SIP_ASSERT(sip1221_als_read_raw_data(i2c_info, als_info));
    SIP_ASSERT(sip1221_als_auto_gain(i2c_info, als_info));
    SIP_ASSERT(sip1221_als_compute_lux(als_info));
		
    *lux = als_info->out.lux;
    return 0;
}

int sip1221_als_factory_sample(uint16_t *ch0_raw,uint16_t *ch1_raw)
{
    uint8_t gain = 0;
    struct sip1221_device *dev = get_sip1221_device();
    sip_i2c_info_t *i2c_info = &dev->iinfo;
    sip_als_info_t *als_info = &dev->ainfo;

    SIP_ASSERT(sip1221_als_get_int_stat(i2c_info, als_info));
    SIP_ASSERT(sip1221_als_get_stat(i2c_info, als_info));
    SIP_ASSERT(sip1221_als_analyse_stat(i2c_info, als_info));
    SIP_ASSERT(sip1221_als_read_raw_data(i2c_info, als_info));
    SIP_ASSERT(i2c_info->read_reg(SIP1221_ALS0_1_GAIN, &gain));
		logi("sip1221_als_factory_sample gain= %f", als_gains[gain & 0x0f].rgain);
	
    *ch0_raw = als_info->out.raw[0];
    *ch1_raw = als_info->out.raw[1];
    return 0;
}

int sip1221_als_irq_sample(sip_i2c_info_t *i2c_info, sip_als_info_t *als_info)
{
    if (als_info->conf.is_dri)
    {
        if (als_info->stat.int_status & ALS_INT)
        {
            als_info->stat.int_status = 0;
            SIP_ASSERT(sip1221_als_get_stat(i2c_info, als_info));
            SIP_ASSERT(sip1221_als_analyse_stat(i2c_info, als_info));
            SIP_ASSERT(sip1221_als_read_raw_data(i2c_info, als_info));
            SIP_ASSERT(sip1221_als_auto_gain(i2c_info, als_info));
            SIP_ASSERT(sip1221_als_update_threhold(i2c_info, als_info));
            SIP_ASSERT(sip1221_als_compute_lux(als_info));
        }
    }
    return 0;
}

int sip1221_als_int_sample(float *lux)
{
    struct sip1221_device *dev = get_sip1221_device();
    sip_i2c_info_t *i2c_info = &dev->iinfo;
    sip_als_info_t *als_info = &dev->ainfo;
    SIP_ASSERT(sip1221_get_int_status(i2c_info, als_info));
    SIP_ASSERT(sip1221_als_irq_sample(i2c_info, als_info));

    *lux = als_info->out.lux;
    return 0;
}


/*****ALS Part End*******/
int sip1221_sensor_init(void)
{
    struct sip1221_device *dev = get_sip1221_device();
    sip_i2c_info_t *i2c_info = &dev->iinfo;
    sip_als_info_t *als_info = &dev->ainfo;

    sip_attach_address(SIP_DEVICE_ADDR);
    sip1221_soft_reset(i2c_info);
    mdelay(1);
	
		if (sip1221_als_checktrim(i2c_info)) {
        logi("sip1221 check trim failed");
        return -1;
    }

    sip1221_als_func_init(i2c_info, als_info);
    return 0;
}

int sip1221_updata_als_cali(float *data, int len)
{
    struct sip1221_device *dev = get_sip1221_device();
    sip_als_info_t *als_info = &dev->ainfo;

    if (data == NULL)
        return -1;

    als_info->cali.light_ch0_coef = data[0];
    als_info->cali.light_ch1_coef = data[1];

    return 0;
}

int sip1221_updata_backlight(uint16_t level)
{
    struct sip1221_device *dev = get_sip1221_device();
    sip_als_info_t *als_info = &dev->ainfo;
    als_info->tmp.brightness = level;
    return 0;
}