#include <string.h>
#include "storage_config.h"
#include "nand_spi.h"
#include "dhara_port.h"
#include "../external/dhara/dhara/error.h"
#include "../external/dhara/dhara/nand.h"

struct dhara_nand g_dhara_nand;

static int ilog2_u32(uint32_t v) {
    int r = -1;
    while (v) { v >>= 1; r++; }
    return r;
}

static inline uint32_t subpages_per_phys_page(void) {
    return (uint32_t)NAND_PAGE_SIZE >> DHARA_LOG2_PAGE_SIZE;
}

static inline int log2_subpages_per_phys_page(void) {
    return ilog2_u32(subpages_per_phys_page());
}

static inline void dhara_page_to_row_col(const struct dhara_nand *n,
                                         dhara_page_t p,
                                         uint32_t *row_out,
                                         uint16_t *col_out) {
    const int l2_spp = log2_subpages_per_phys_page();
    const uint32_t spp = (1u << l2_spp);
    const uint32_t row = (uint32_t)(p >> l2_spp);
    const uint16_t col = (uint16_t)((p & (spp - 1)) << n->log2_page_size);
    if (row_out) *row_out = row;
    if (col_out) *col_out = col;
}

int dhara_port_init(void) {
    // Validate geometry: pages per block (logical) must be power of two
    const uint32_t spp = subpages_per_phys_page();
    if ((spp == 0) || ((spp & (spp - 1)) != 0)) return -1;
    const uint32_t ppb_logical = (uint32_t)NAND_PAGES_PER_BLOCK * spp; // e.g., 64 * 4 = 256
    if ((ppb_logical & (ppb_logical - 1)) != 0) return -1;

    g_dhara_nand.log2_page_size = DHARA_LOG2_PAGE_SIZE;
    g_dhara_nand.log2_ppb       = ilog2_u32(ppb_logical);
    g_dhara_nand.num_blocks     = NAND_BLOCKS;
    return 0;
}

int dhara_nand_is_bad(const struct dhara_nand *n, dhara_block_t b) {
    (void)n;
    return nand_is_bad((uint32_t)b) > 0 ? 1 : 0;
}

void dhara_nand_mark_bad(const struct dhara_nand *n, dhara_block_t b) {
    (void)n;
    (void)nand_mark_bad((uint32_t)b);
}

int dhara_nand_erase(const struct dhara_nand *n, dhara_block_t b, dhara_error_t *err) {
    (void)n;
    if (nand_block_erase((uint32_t)b) < 0) {
        dhara_set_error(err, DHARA_E_BAD_BLOCK);
        return -1;
    }
    return 0;
}

int dhara_nand_prog(const struct dhara_nand *n, dhara_page_t p, const uint8_t *data, dhara_error_t *err) {
    if (!data) return -1;
    uint32_t row; uint16_t col;
    dhara_page_to_row_col(n, p, &row, &col);
    const uint32_t len = (1u << n->log2_page_size);
    if (nand_prog_load(col, data, len) < 0) { dhara_set_error(err, DHARA_E_BAD_BLOCK); return -1; }
    if (nand_prog_exec(row) < 0)            { dhara_set_error(err, DHARA_E_BAD_BLOCK); return -1; }
    return 0;
}

int dhara_nand_is_free(const struct dhara_nand *n, dhara_page_t p) {
    uint32_t row; uint16_t col;
    dhara_page_to_row_col(n, p, &row, &col);
    // Read full page-sized region and verify 0xFF
    uint8_t buf[1u << 9]; // default 512B; still works for larger if DHARA_LOG2_PAGE_SIZE changes up to 512
    const uint32_t page_len = (1u << n->log2_page_size);
    uint32_t remaining = page_len;
    uint16_t c = col;
    if (nand_page_read_to_cache(row) < 0) return 0;
    while (remaining) {
        uint32_t chunk = remaining > sizeof(buf) ? sizeof(buf) : remaining;
        nand_ecc_status_t ecc;
        if (nand_read_from_cache(c, buf, chunk, &ecc) < 0) return 0;
        for (uint32_t i = 0; i < chunk; ++i) if (buf[i] != 0xFF) return 0;
        remaining -= chunk; c += (uint16_t)chunk;
    }
    return 1;
}

int dhara_nand_read(const struct dhara_nand *n, dhara_page_t p,
                    size_t offset, size_t length,
                    uint8_t *data, dhara_error_t *err) {
    if (!data) return -1;
    if ((offset + length) > (size_t)(1u << n->log2_page_size)) return -1;
    uint32_t row; uint16_t col;
    dhara_page_to_row_col(n, p, &row, &col);
    if (nand_page_read_to_cache(row) < 0) { dhara_set_error(err, DHARA_E_BAD_BLOCK); return -1; }
    nand_ecc_status_t ecc;
    if (nand_read_from_cache((uint16_t)(col + offset), data, (uint32_t)length, &ecc) < 0) {
        dhara_set_error(err, DHARA_E_BAD_BLOCK);
        return -1;
    }
    if (ecc == NAND_ECC_UNCORRECTABLE) {
        dhara_set_error(err, DHARA_E_ECC);
        return -1;
    }
    return 0;
}

int dhara_nand_copy(const struct dhara_nand *n, dhara_page_t src, dhara_page_t dst, dhara_error_t *err) {
    const uint32_t len = (1u << n->log2_page_size);
    uint8_t buf[1u << 9]; // fits default 512B logical page
    // If logical page size differs, read/write in chunks
    uint32_t done = 0;
    while (done < len) {
        const uint32_t chunk = (len - done) > sizeof(buf) ? sizeof(buf) : (len - done);
        if (dhara_nand_read(n, src, done, chunk, buf, err) < 0) return -1;
        // Program must be contiguous from offset 0; Dhara calls prog() with full page normally.
        if (done == 0 && chunk == len) {
            // Fast path: full page
            if (dhara_nand_prog(n, dst, buf, err) < 0) return -1;
        } else {
            // For non-512 sizes, accumulate into a page buffer (not expected in our config)
            // Simplify: require 512B page here
            if (len != sizeof(buf)) {
                // Fallback: assemble full page
                uint8_t full[1u << 9];
                uint32_t acc = 0;
                while (acc < len) {
                    const uint32_t c2 = (len - acc) > sizeof(full) ? sizeof(full) : (len - acc);
                    if (dhara_nand_read(n, src, acc, c2, full + acc, err) < 0) return -1;
                    acc += c2;
                }
                return dhara_nand_prog(n, dst, full, err);
            }
        }
        done += chunk;
    }
    return 0;
}

