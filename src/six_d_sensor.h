// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the use
//   or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------


#ifndef SIX_D_SENSOR_H
#define SIX_D_SENSOR_H


#include "lsm6dso.h"
#include "am_mcu_apollo.h"
#include "am_bsp.h"
#include "am_util.h"
#include "am_hal_global.h"
#include "am_hal_gpio.h"
#include "i2c.h"


#define LSM6DSO_I2C_ADDR  0x6A //<< 1  // 7位地址左移1位，符合HAL API要求

extern stmdev_ctx_t dev_ctx;

extern LSM6DSO_Object_t lsm6dso_obj;

void lsm6dso_init_with_int1(void);

void lsm6dso_init_with_obj(void);


#endif //SIX_D_SENSOR_H