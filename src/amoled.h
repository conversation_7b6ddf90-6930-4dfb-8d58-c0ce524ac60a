// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the
//   use or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------

#ifndef AMOLED_H
#define AMOLED_H

#include "am_bsp.h"
#include "am_hal_global.h"
#include "am_hal_gpio.h"
#include "am_mcu_apollo.h"
#include "am_util.h"

#define ROW 340
#define COL 340

extern void* g_Mspi1Handle;
extern const uint16_t image_340x340[340][340];

uint32_t AMOLED_POWER_EN(void);
uint32_t mspi1_init_spi_mode(void);
uint32_t mspi1_write(uint8_t instr, uint32_t addr, uint8_t* data,
                     uint32_t length);
uint32_t mspi1_read(uint8_t instr, uint32_t addr, uint8_t* data,
                    uint32_t length);

void AMOLED_Init(void);
void AMOLED_Block_Write(uint16_t Xstart, uint16_t Xend, uint16_t Ystart,
                        uint16_t Yend);
void SPI_1L_SendData(uint16_t dat);
void SPI_WriteComm(uint16_t regval);
void SPI_ReadComm(uint16_t regval);
void SPI_WriteComm_QSPI(uint16_t regval);
void SPI_WriteData(uint16_t val);
void Write_Disp_Data(uint16_t val);
void QSPI_WriteData(uint16_t val);
void SPI_4wire_data_1wire_Addr(uint16_t First_Byte, uint16_t Addr);
void SPI_4wire_data_4wire_Addr(uint16_t First_Byte, uint16_t Addr);
void SPI_4W_DATA_4W_ADDR_START(void);
void SPI_4W_DATA_1W_ADDR_START(void);
void SPI_4W_DATA_1W_ADDR_END(void);
void DM_Clear(uint16_t color);
void show_photo(const uint16_t color[340][340]);

#endif  // AMOLED_H