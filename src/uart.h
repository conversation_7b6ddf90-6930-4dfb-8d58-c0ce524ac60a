// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the
//   use or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------

#ifndef UART_H
#define UART_H

#include "am_bsp.h"
#include "am_hal_global.h"
#include "am_hal_gpio.h"
#include "am_mcu_apollo.h"
#include "am_util.h"

#define UART0_MODULE 0  // ?? IOM0
#define UART1_MODULE 1  // ?? IOM1
#define UART2_MODULE 2  // ?? IOM2
#define UART3_MODULE 3  // ?? IOM3

#define RX_BUFFER_SIZE 64
#define TX_BUFFER_SIZE 64

extern uint8_t g_uart_tx_buffer[TX_BUFFER_SIZE];
extern uint8_t g_uart_rx_buffer[RX_BUFFER_SIZE];
extern uint8_t g_uart_rx_data[RX_BUFFER_SIZE];
extern volatile uint32_t g_uart_rx_len;
extern uint8_t led_flag;

extern void* phUART;

extern uint8_t g_pui8TxBuffer[256];
extern uint8_t g_pui8RxBuffer[2];

void error_handler(uint32_t ui32ErrorStatus);
void uart_print(char* pcStr);
uint32_t UART_init(void);

#endif  // UART_H