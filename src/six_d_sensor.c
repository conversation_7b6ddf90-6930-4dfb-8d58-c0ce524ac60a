// --------------------------------------------------------------------
// Copyright (c) 2025 by happystone Inc.,Seattle,USA.
// All rights reserved.
// --------------------------------------------------------------------
//
// Permission:
//
//   The user is granted permission to use and modify this code
//   for all boards based on the Ambiq Apollo4 MCU developed by Happystone Inc.
//   Any other use of this code-including but not limited to selling,
//   duplicating, or modifying any part of it-is strictly prohibited.
//
// Disclaimer:
//
//   This C/C++ source code is intended as a design reference
//   which illustrates how these types of functions can be implemented.
//   It is the user's responsibility to verify their design for
//   consistency and functionality through the use of formal
//   verification methods.  happystone Inc. provides no warranty regarding the use
//   or functionality of this code.
//
// --------------------------------------------------------------------
//
//                     happystone Inc.
//                     Seattle
//
//
//
//
//
// --------------------------------------------------------------------


#include "six_d_sensor.h"

int32_t platform_write(void *handle, uint8_t reg, uint8_t *bufp, uint16_t len)
{
    return i2c_write_bytes(&g_pIOMHandle_2, LSM6DSO_I2C_ADDR, reg, bufp, len);
	
}

int32_t platform_read(void *handle, uint8_t reg, uint8_t *bufp, uint16_t len)
{
    return i2c_read_bytes(&g_pIOMHandle_2, LSM6DSO_I2C_ADDR, reg, bufp, len);
	
}

void platform_delay(uint32_t ms)
{
	am_util_delay_ms(ms);
}


stmdev_ctx_t dev_ctx;

void lsm6dso_init_with_int1(void)
{
    uint8_t whoamI;
    lsm6dso_pin_int1_route_t int1_route;
    uint8_t rst;

    // 配置驱动函数
    dev_ctx.write_reg = platform_write;
    dev_ctx.read_reg = platform_read;
    dev_ctx.handle = NULL;

    // 延时初始化
    platform_delay(10);

    // 检查设备 ID
    lsm6dso_device_id_get(&dev_ctx, &whoamI);
    if (whoamI != LSM6DSO_ID)
    {
      am_util_stdio_printf("6轴传感器ID不匹配!\r\n");  
			while (1);  // ID 不匹配
    }
		else
		{
			am_util_stdio_printf("6轴传感器ID正确!\r\n");
		}

    // 软复位
    lsm6dso_reset_set(&dev_ctx, PROPERTY_ENABLE);
    do {
        lsm6dso_reset_get(&dev_ctx, &rst);
    } while (rst);

    // 开启 Block Data Update
    lsm6dso_block_data_update_set(&dev_ctx, PROPERTY_ENABLE);

    // 配置加速度计：±2g, 26Hz
    lsm6dso_xl_full_scale_set(&dev_ctx, LSM6DSO_2g);
    lsm6dso_xl_data_rate_set(&dev_ctx, LSM6DSO_XL_ODR_26Hz);

    // 设置6D阈值为60°
    //lsm6dso_6d_threshold_set(&dev_ctx, LSM6DSO_DEG_60);
		lsm6dso_6d_threshold_set(&dev_ctx, LSM6DSO_DEG_50);

    // 配置中断输出为脉冲
    lsm6dso_int_notification_set(&dev_ctx, LSM6DSO_ALL_INT_PULSED);

    // 配置INT1引脚输出6D中断
    lsm6dso_pin_int1_route_get(&dev_ctx, &int1_route);
    int1_route.six_d  = PROPERTY_ENABLE;
    lsm6dso_pin_int1_route_set(&dev_ctx, int1_route);
}


int32_t platform_read_obj(uint16_t addr, uint16_t reg, uint8_t* bufp, uint16_t len)
{
	return i2c_write_bytes(&g_pIOMHandle_2, addr, reg, bufp, len);
}
int32_t platform_write_obj(uint16_t addr, uint16_t reg, uint8_t* bufp, uint16_t len)
{
	return i2c_read_bytes(&g_pIOMHandle_2, addr, reg, bufp, len);
}

int32_t IO_Init_obj(void)
{
	return 0;
}



LSM6DSO_Object_t lsm6dso_obj;


// 初始化函数
void lsm6dso_init_with_obj(void)
{
    LSM6DSO_IO_t io_ctx;
    uint8_t id;

    // 步骤 1: 填写 I/O 函数和设备信息
    io_ctx.BusType     = LSM6DSO_I2C_BUS;
    io_ctx.Address     = 0x6A;  // ST 驱动中为 8-bit 地址
    io_ctx.Init        = IO_Init_obj;
    io_ctx.DeInit      = NULL;
    io_ctx.ReadReg     = platform_read_obj;
    io_ctx.WriteReg    = platform_write_obj;
    io_ctx.GetTick     = NULL;
    io_ctx.Delay       = platform_delay;

    // 步骤 2: 注册 I/O 接口到设备对象
    if (LSM6DSO_RegisterBusIO(&lsm6dso_obj, &io_ctx) != LSM6DSO_OK)
    {
        am_util_stdio_printf("RegisterBusIO failed\r\n");
        //return;
    }

    // 步骤 3: 初始化传感器
    if (LSM6DSO_Init(&lsm6dso_obj) != LSM6DSO_OK)
    {
        am_util_stdio_printf("LSM6DSO init failed\r\n");
        //return;
    }

    // 步骤 4: 读取 ID 确认
    if (LSM6DSO_ReadID(&lsm6dso_obj, &id) != LSM6DSO_OK || id != LSM6DSO_ID)
    {
        am_util_stdio_printf("LSM6DSO ID check failed\r\n");
        //return;
    }

    // 步骤 5: 启用加速度计与陀螺仪
    LSM6DSO_ACC_Enable(&lsm6dso_obj);
    LSM6DSO_GYRO_Enable(&lsm6dso_obj);

    am_util_stdio_printf("LSM6DSO 初始化完成！\r\n");
}

