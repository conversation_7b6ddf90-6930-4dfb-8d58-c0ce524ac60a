## LittleFS + Dhara + SPI-NAND (MSPI2) Integration Design

### Overview
This document describes the storage stack integrated in this firmware to manage the on‑board 2Gbit SPI‑NAND connected via MSPI2 on Ambiq Apollo4 Blue Plus.

Layers (top → bottom):
- Application code (e.g., CLI shell commands)
- LittleFS block device driver (lfs_dhara_bd)
- Dhara FTL (wear‑leveling, bad‑block management)
- Dhara NAND port (dhara_nand_* implementation)
- SPI‑NAND driver (nand_spi)
- Ambiq MSPI2 HAL
- NAND device

All geometry and policy are configurable in a single header: src/storage_config.h. On‑die ECC can be enabled/disabled at runtime.

### Data flow for a typical file write
1) App writes to a file using LittleFS APIs. Example: lfs_file_write().
2) LittleFS calls its configured block device callbacks:
   - prog(c, block, off, buf, size) where block/offset are 512‑byte aligned in our design.
3) lfs_dhara_bd maps each 512B block to one Dhara logical page and calls dhara_map_write().
4) <PERSON>hara translates logical page to a physical location (wear‑leveled) and calls dhara_nand_prog().
5) dhara_nand_prog() computes the physical page row/column and issues:
   - nand_prog_load(col, data, len)
   - nand_prog_exec(row)
6) nand_spi performs MSPI PIO writes to the SPI‑NAND with PROG_LOAD (+ WRITE_ENABLE) and PROG_EXEC, then polls status until OIP clears; status bits indicate ECC/bad‑block/program fail.

Reads follow the inverse path via lfs_bd_read → dhara_map_read → dhara_nand_read → (PAGE_READ_TO_CACHE + READ_FROM_CACHE).

Block erase is a no‑op in lfs_dhara_bd (FTL mode). Dhara handles erases internally during GC.

### Geometry, alignment, and sizes
- NAND (defaults, configurable):
  - page 2048B, OOB 64B, 64 pages/block, 2048 blocks
- Dhara logical page size: 512B (DHARA_LOG2_PAGE_SIZE = 9)
  - 4 subpages per physical page; log2_ppb = log2(64*4) = 8
- LittleFS block/prog/read size: 512B (block_cycles = -1 to let Dhara do WL)
- Capacity reported to LittleFS: dhara_map_capacity() logical sectors.

### ECC strategy (on‑die ECC)
- ECC feature register address and masks are configurable (defaults match common SPI‑NAND parts):
  - FEATURE_ADDR_ECC = 0xB0, ECC_EN_MASK = 0x01
  - Status ECC field mask 0x30 (CORR=0x10, UNC=0x20)
- ECC enable/disable is controlled at runtime in fs_service_hw_init(enable_ecc).
- When enabled, read/program operations report ECC status via the status register after PAGE_READ_TO_CACHE or PROG_EXEC; dhara treats UNC as read error.

### Bad‑block management
- nand_is_bad() checks first two pages’ spare[0] marker (configurable) via OOB column = NAND_PAGE_SIZE.
- nand_mark_bad() sets marker in spare[0] of first page.
- Dhara also handles remapping and avoiding bad blocks; it calls our is_bad/mark_bad.

### Concurrency and mutex design
- Filesystem access is serialized by a mutex owned by fs_service.
- Public fs_service APIs that mutate or read the filesystem take the mutex internally.
- Additionally, fs_acquire()/fs_release() APIs allow advanced clients to bracket multiple LittleFS calls atomically.
- CLI commands always use fs_service wrappers (or fs_acquire/release) so concurrent callers (GUI tasks, etc.) are safe.
- Mount/format/unmount are also serialized by the same mutex; callers must avoid reentrant mount/format.

### Error handling
- All low‑level operations return negative error codes; LittleFS expects 0 or LFS_ERR_* codes from the BD layer and translates to its own errors.
- Dhara errors are set using dhara_error_t and mapped to LFS_ERR_IO by the BD wrapper.
- Uncorrectable ECC returns DHARA_E_ECC → upper layers fail the read and may trigger Dhara relocations.

### Configuration points
- src/storage_config.h: NAND geometry, feature/status fields, timeouts, DHARA_LOG2_PAGE_SIZE, OOB policy
- src/lfs_dhara_bd.c: cache sizes, lookahead, block_cycles
- Runtime ECC: fs_service_hw_init(enable_ecc)

### Diagrams (logical)
- Path for write:
  App → LittleFS → lfs_dhara_bd.prog → dhara_map_write → dhara_nand_prog → nand_prog_load/exec → MSPI2 → NAND
- Path for read:
  App → LittleFS → lfs_dhara_bd.read → dhara_map_read → dhara_nand_read → page_to_cache + read_from_cache → MSPI2 → NAND

### Testing strategy
- Bare‑metal fs_test app validates create/write/read quickly.
- CLI shell (see shell_design.md) provides on‑target interactive validation: mkfs, mount, ls, cat, write, rm, mkdir, stat, df.

### Future refinements
- Switch to quad mode and device‑specific fast‑read opcodes when the real part is known.
- Optional background GC triggers via a low‑priority task.
- Partitioning support if needed later (currently exposing full device).



### Common functionality in LittleFS and Dhara, and how we avoid redundant overhead

LittleFS (a small POSIX-like filesystem) and Dhara (an FTL for raw NAND) each provide robustness and wear-related features. Stacking them naïvely can double up work. Our integration intentionally assigns responsibilities to minimize overhead while preserving reliability.

1) Wear leveling
- Overlap: LittleFS does copy-on-write metadata movement and optional wear-leveling of metadata via block_cycles. Dhara performs full-device wear leveling and garbage collection across logical sectors.
- Our choices to avoid duplication:
  - lfs_dhara_bd sets cfg.block_cycles = -1, effectively disabling LittleFS’s additional wear-leveling policy. Dhara is the single source of wear leveling.
  - We present 512B logical blocks to LittleFS (read_size = prog_size = block_size = 512). This aligns 1:1 with Dhara’s logical sector (DHARA_LOG2_PAGE_SIZE=9). Therefore, LittleFS never needs sub-sector read-modify-write cycles; Dhara owns placement/relocation.

2) Erase management and TRIM/discard
- Overlap: LittleFS expects a block device erase primitive, while Dhara manages erases internally during GC.
- Our choices:
  - lfs_dhara_bd.erase is a no-op. We don’t issue device erases from the filesystem layer; Dhara triggers erases as needed during GC. This prevents redundant erases and long erase latencies in the FS critical path.
  - Optional future optimization: map erase/trim events (when we know a sector is no longer used) into dhara_map_trim() hints. This can accelerate Dhara’s GC without changing correctness. The current design keeps it simple (no-op) to minimize complexity and blocking times.

3) Bad-block management
- Overlap: Dhara is designed to handle bad blocks; LittleFS assumes a reliable block device and does not manage bad-block tables.
- Our choices:
  - Bad-block discovery/marking is implemented in the NAND port (nand_is_bad/nand_mark_bad). Dhara calls these hooks and maintains the logical mapping to avoid defective blocks. LittleFS stays agnostic and sees a contiguous logical space. No duplication here.

4) Journaling / crash-consistency
- Overlap: LittleFS uses a small COW/commit scheme with CRCs to maintain metadata consistency across power-loss. Dhara has its own mapping journal to ensure the FTL map is atomically updated.
- Our choices:
  - We accept the minimal double-commit pattern (LittleFS commit + Dhara map update). This is expected in FS-over-FTL stacks and provides robust end-to-end consistency at two layers.
  - Overhead kept small by:
    - 512B program units to avoid partial-page RMW.
    - Small caches (cache_size = 512) and modest lookahead (128) to limit RAM and IO churn while keeping performance reasonable.
    - erase is a no-op, removing a major latency source from the FS path.

5) Logical addressing and mapping
- Overlap: LittleFS maps file offsets to logical blocks; Dhara maps logical blocks (sectors) to physical NAND pages.
- Our choices:
  - Enforce strict alignment and size: lfs_dhara_bd rejects unaligned accesses; LittleFS is configured to only issue 512B-aligned operations. That guarantees Dhara always receives sector-aligned reads/writes and can optimize placement without extra RMW cycles.

6) ECC and data integrity
- Overlap: Filesystems often add checksums; FTLs/devices add ECC. LittleFS already checks metadata with CRCs; Dhara can surface ECC failures.
- Our choices:
  - Use the device’s on-die ECC (configurable at runtime). Reads/programs report ECC status via status register; uncorrectable errors propagate as IO errors up to LittleFS. We don’t add software ECC on top, avoiding CPU overhead and duplicate parity storage.

7) Free space and garbage collection
- Overlap: LittleFS reclaims filesystem-level space by moving metadata/data; Dhara reclaims physical blocks via GC.
- Our choices:
  - Keep LittleFS’s movement minimal by disabling extra wear-leveling (block_cycles = -1) and using 512B blocks. Dhara’s GC handles wear/distribution across the full device efficiently.

8) Concurrency and flush semantics
- Overlap: Both layers track state that needs flushing to become durable (LittleFS commits; Dhara map syncs).
- Our choices:
  - lfs_dhara_bd.sync calls dhara_map_sync() to push FTL state to NAND when LittleFS requests a sync. The fs_service mutex serializes all FS operations (including syncs) to avoid interleaving that could cause extra commits.

Summary of overhead-avoidance tactics (concrete configuration)
- lfs_dhara_bd.c:
  - cfg.read_size = cfg.prog_size = cfg.block_size = 512
  - cfg.block_cycles = -1 (delegate wear-leveling to Dhara)
  - cfg.erase = no-op (Dhara handles erases internally)
  - cfg.sync → dhara_map_sync()
  - cache_size = 512, lookahead_size = 128 (tunable)
- dhara_port: exposes 512B sectors and maps cleanly to NAND page/row operations; Dhara handles bad blocks and GC.
- nand_spi: uses feature/status to control and read on-die ECC; no software ECC added.

Tradeoffs
- Double-commit (LittleFS commit + Dhara journal) is inherent to FS-over-FTL stacks; in practice, with 512B units and no FS-level erase, the added latency is small relative to NAND page/cache ops.
- Setting block_cycles = -1 prioritizes Dhara for wear-leveling; metadata hot-spots in LittleFS will be primarily mitigated by Dhara’s placement. If future profiling shows metadata hotspots, we can raise block_cycles slightly to spread LittleFS metadata moves without significantly duplicating FTL wear-leveling.


### Erase/commit visualization and expected latency per 512B write

The sequence below shows what happens for a single 512B data write from the app, assuming on‑die ECC enabled and steady‑state (no immediate GC):

- LittleFS layer:
  - Issues prog(block, off=0, size=512) for the data.
  - Later, issues small metadata updates (directory entry/inode) and a commit record.
  - Erase: none (erase is a no‑op in our BD).
- Dhara layer:
  - Allocates a fresh logical sector → maps it to a free physical location (copy‑on‑write).
  - Writes data sector; updates its mapping journal/metadata.
  - Erase: deferred; happens during GC when needed (amortized).
- SPI‑NAND device (PIO MSPI transfers):
  - WRITE_ENABLE
  - PROG_LOAD (cache program of 512B into the page buffer)
  - PROG_EXEC (row/page program) + status poll until OIP clears; check P_FAIL/ECC bits
  - No block erase on the critical path for this write.

Typical latencies on common SPI‑NAND (order‑of‑magnitude; device‑specific):
- PROG_EXEC (page program): ~200–800 µs (applies even for partial‑page program)
- READ_TO_CACHE: ~25–100 µs (for reads, not used on pure write path)
- BLOCK_ERASE: ~2–5 ms (only during GC; amortized over many writes)
- STATUS polls: a few µs total

Therefore, the steady‑state per‑512B write path commonly incurs:
- 1× program op for the data sector (Dhara)
- +1–2× small program ops for LFS metadata/commit (depends on file/dir structure and caching)
- 0× erases in the foreground; GC erases occur intermittently and amortize across many writes

A compact picture (per 512B write, steady‑state):
- LFS: data prog (sometimes merged), metadata prog(s), no erase
- Dhara: map update (journal), no erase foreground; background GC may read‑copy‑erase blocks occasionally
- NAND: ~1–3 program ops now; ~0 erase now; occasional later erase (2–5 ms) when GC triggers

### Why “double‑commit” is inherent to FS‑over‑FTL

- Two distinct consistency domains:
  - Filesystem consistency (directory entries, file metadata, allocation state): LittleFS uses COW + commit records and CRCs to ensure it can recover after power loss.
  - Mapping consistency (logical‑to‑physical translation): Dhara must persist its mapping updates atomically or be able to recover them.
- Decoupled layers: The FS has no visibility into the FTL’s map journal, and the FTL has no knowledge of FS metadata lifecycles. Each must commit its own critical metadata.
- Eliminating one commit would require tight co‑design where FS and FTL share a unified journal/transaction mechanism. That breaks modularity and locks you to a single combined implementation.
- In practice, the added cost is small with our configuration:
  - 512B alignment avoids RMW cycles.
  - No FS‑level erase.
  - Small metadata writes.
  - Dhara’s journal writes are compact and can be coalesced across multiple sector writes.

### Why FS‑over‑FTL instead of LittleFS directly on raw NAND, or FTL alone

- LittleFS directly on raw NAND (without FTL):
  - LittleFS is designed for generic block devices. It does not handle bad‑block tables, device‑wide wear leveling, or on‑die ECC policy.
  - Raw NAND requires: bad‑block management, wear leveling across the entire device, and careful program/erase cycle balancing. Re‑implementing these in the FS layer defeats LittleFS’s simplicity and risks correctness.
  - Result: High engineering risk and likely lower lifetime/endurance.
- FTL alone (without a filesystem):
  - Dhara provides a logical sector device, not hierarchical files, directories, timestamps, etc.
  - You would need to build file semantics on top of key‑value or sector APIs, re‑inventing allocation, naming, and crash‑safe metadata.
- FS over FTL benefits:
  - Clear separation of concerns: Dhara owns NAND quirks (bad blocks, wear, GC), LittleFS owns file/dir semantics and metadata integrity.
  - Tunable performance: 512B sectors, no FS erase, and disabled FS wear leveling minimize overlap and overhead.
  - Portability: Either layer can be swapped or tuned independently (e.g., different FTL or different FS) with well‑defined interfaces.

### When to revisit this design

- If profiling shows metadata hot‑spots: consider a small, finite block_cycles in LittleFS (e.g., 100–500) to slightly diversify metadata locations while keeping Dhara as the primary WL engine.
- If real NAND shows slow partial‑page programs: consider buffering to full 2KB pages (4×512B) for some workloads to reduce program op count.
- If on‑die ECC behavior or OOB layout requires app‑level checks: add optional software checksums on user data at the FS layer (opt‑in; default remains off to avoid CPU/storage overhead).
- If you later need strict write‑amplification bounds for a specific workload: we can add trim hints from lfs_dhara_bd to Dhara, or consider a unified FS+FTL journal (at cost of modularity).
