## UART Shell using FreeRTOS-Plus-CLI

### Goals
- Provide an interactive shell over UART using FreeRTOS-Plus-CLI.
- Demonstrate filesystem features (LittleFS over Dhara) with simple commands.
- Ensure concurrency safety by routing all filesystem access through fs_service mutex-guarded APIs.

### Architecture
- CLI task: a FreeRTOS task that:
  - Initializes UART (if not already done by board_init) and the CLI interpreter
  - Registers a set of filesystem commands
  - Reads command lines from UART, feeds them into FreeRTOS_CLIProcessCommand(), and writes responses back to UART
- IO: Use existing UART module (src/uart.c) for output via am_util_stdio_printf and a simple input path:
  - The current UART ISR fills g_uart_rx_data and sets g_uart_rx_len when a newline arrives
  - The CLI task polls g_uart_rx_len; when > 0, it copies the line, clears g_uart_rx_len, and processes the command
  - This approach avoids blocking on UART input and keeps the design simple
- Registration: We register commands at startup via FreeRTOS_CLIRegisterCommand()

### Concurrency model
- CLI commands call fs_service APIs that internally acquire the filesystem mutex
- For multi-step commands (e.g., ls and cat that iterate), the command either:
  - Uses a single fs_acquire()/fs_release() bracket, or
  - Uses fs_service helpers that lock internally per function
- CLI task priority is low; it should not interfere with display/GUI tasks.

### Command set (initial)
- mount                   Mount the filesystem (format not implied)
- umount                  Unmount the filesystem
- mkfs                    Format the device (erases existing FS) then mount
- ls [path]               List directory (default "/")
- cat <path>              Print file contents
- write <path> <text>     Create/truncate and write text (no spaces preserved beyond first unless quoted; see notes)
- append <path> <text>    Append text
- rm <path>               Remove file
- mkdir <path>            Create directory
- stat <path>             Show size/type
- df                      Show total/used/free in sectors and bytes

Notes:
- Paths are LittleFS paths; they need not start with '/'.
- For simplicity, write/append treat the remainder of the line after the path as the text payload (without needing shell-side quoting). Spaces are preserved.

### Task integration
- The CLI task is created in setup_task() (src/rtos.c) alongside other tasks
- UART is already initialized during board_init(); shell prints a welcome banner and a simple prompt "> "
- Stack size: 2 KB (configurable) with a 512 byte command buffer and 512 byte output buffer

### Memory and buffers
- Command input buffer: 256512 bytes (configurable)
- Output buffer: 512 bytes for FreeRTOS_CLIProcessCommand (called repeatedly until empty)
- Minimal heap usage; the commands avoid dynamic allocation and reuse static buffers

### Extensibility
- Additional commands can be added by defining new FreeRTOS_CLICommandDefinition_t and registering them in cli_shell_init()
- Non-filesystem commands (e.g., nand info, ecc on/off) can be added easily and will also use fs_service where applicable

