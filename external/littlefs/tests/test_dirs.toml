[cases.test_dirs_root]
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_many_creation]
defines.N = 'range(3, 100, 3)'
if = 'N < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "dir%03d", i);
        lfs_mkdir(&lfs, path) => 0;
    }
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "dir%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_DIR);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_many_removal]
defines.N = 'range(3, 100, 11)'
if = 'N < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "removeme%03d", i);
        lfs_mkdir(&lfs, path) => 0;
    }
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "removeme%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_DIR);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs);

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "removeme%03d", i);
        lfs_remove(&lfs, path) => 0;
    }
    lfs_unmount(&lfs);

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_many_rename]
defines.N = 'range(3, 100, 11)'
if = 'N < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "test%03d", i);
        lfs_mkdir(&lfs, path) => 0;
    }
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "test%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_DIR);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs);

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char oldpath[128];
        char newpath[128];
        sprintf(oldpath, "test%03d", i);
        sprintf(newpath, "tedd%03d", i);
        lfs_rename(&lfs, oldpath, newpath) => 0;
    }
    lfs_unmount(&lfs);

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "tedd%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_DIR);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs);
'''

[cases.test_dirs_many_reentrant]
defines.N = [5, 11]
if = 'BLOCK_COUNT >= 4*N'
reentrant = true
defines.POWERLOSS_BEHAVIOR = [
    'LFS_EMUBD_POWERLOSS_NOOP',
    'LFS_EMUBD_POWERLOSS_OOO',
]
code = '''
    lfs_t lfs;
    int err = lfs_mount(&lfs, cfg);
    if (err) {
        lfs_format(&lfs, cfg) => 0;
        lfs_mount(&lfs, cfg) => 0;
    }

    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hi%03d", i);
        err = lfs_mkdir(&lfs, path);
        assert(err == 0 || err == LFS_ERR_EXIST);
    }

    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hello%03d", i);
        err = lfs_remove(&lfs, path);
        assert(err == 0 || err == LFS_ERR_NOENT);
    }

    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hi%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_DIR);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;

    for (int i = 0; i < N; i++) {
        char oldpath[128];
        char newpath[128];
        sprintf(oldpath, "hi%03d", i);
        sprintf(newpath, "hello%03d", i);
        // YES this can overwrite an existing newpath
        lfs_rename(&lfs, oldpath, newpath) => 0;
    }

    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hello%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_DIR);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;

    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hello%03d", i);
        lfs_remove(&lfs, path) => 0;
    }

    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_file_creation]
defines.N = 'range(3, 100, 11)'
if = 'N < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "file%03d", i);
        lfs_file_t file;
        lfs_file_open(&lfs, &file, path,
                LFS_O_WRONLY | LFS_O_CREAT | LFS_O_EXCL) => 0;
        lfs_file_close(&lfs, &file) => 0;
    }
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "file%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_REG);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs);
'''

[cases.test_dirs_file_removal]
defines.N = 'range(3, 100, 11)'
if = 'N < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "removeme%03d", i);
        lfs_file_t file;
        lfs_file_open(&lfs, &file, path,
                LFS_O_WRONLY | LFS_O_CREAT | LFS_O_EXCL) => 0;
        lfs_file_close(&lfs, &file) => 0;
    }
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "removeme%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_REG);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs);

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "removeme%03d", i);
        lfs_remove(&lfs, path) => 0;
    }
    lfs_unmount(&lfs);

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_file_rename]
defines.N = 'range(3, 100, 11)'
if = 'N < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "test%03d", i);
        lfs_file_t file;
        lfs_file_open(&lfs, &file, path,
                LFS_O_WRONLY | LFS_O_CREAT | LFS_O_EXCL) => 0;
        lfs_file_close(&lfs, &file) => 0;
    }
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "test%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_REG);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs);

    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < N; i++) {
        char oldpath[128];
        char newpath[128];
        sprintf(oldpath, "test%03d", i);
        sprintf(newpath, "tedd%03d", i);
        lfs_rename(&lfs, oldpath, newpath) => 0;
    }
    lfs_unmount(&lfs);

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "tedd%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_REG);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs);
'''

[cases.test_dirs_file_reentrant]
defines.N = [5, 25]
if = 'N < BLOCK_COUNT/2'
reentrant = true
defines.POWERLOSS_BEHAVIOR = [
    'LFS_EMUBD_POWERLOSS_NOOP',
    'LFS_EMUBD_POWERLOSS_OOO',
]
code = '''
    lfs_t lfs;
    int err = lfs_mount(&lfs, cfg);
    if (err) {
        lfs_format(&lfs, cfg) => 0;
        lfs_mount(&lfs, cfg) => 0;
    }

    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hi%03d", i);
        lfs_file_t file;
        lfs_file_open(&lfs, &file, path, LFS_O_CREAT | LFS_O_WRONLY) => 0;
        lfs_file_close(&lfs, &file) => 0;
    }

    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hello%03d", i);
        err = lfs_remove(&lfs, path);
        assert(err == 0 || err == LFS_ERR_NOENT);
    }

    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hi%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_REG);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;

    for (int i = 0; i < N; i++) {
        char oldpath[128];
        char newpath[128];
        sprintf(oldpath, "hi%03d", i);
        sprintf(newpath, "hello%03d", i);
        // YES this can overwrite an existing newpath
        lfs_rename(&lfs, oldpath, newpath) => 0;
    }

    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hello%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_REG);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;

    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "hello%03d", i);
        lfs_remove(&lfs, path) => 0;
    }

    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_nested]
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "potato") => 0;
    lfs_file_t file;
    lfs_file_open(&lfs, &file, "burito",
            LFS_O_WRONLY | LFS_O_CREAT | LFS_O_EXCL) => 0;
    lfs_file_close(&lfs, &file) => 0;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "potato/baked") => 0;
    lfs_mkdir(&lfs, "potato/sweet") => 0;
    lfs_mkdir(&lfs, "potato/fried") => 0;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "potato") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, ".") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "..") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "baked") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "fried") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "sweet") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;

    // try removing?
    lfs_mount(&lfs, cfg) => 0;
    lfs_remove(&lfs, "potato") => LFS_ERR_NOTEMPTY;
    lfs_unmount(&lfs) => 0;

    // try renaming?
    lfs_mount(&lfs, cfg) => 0;
    lfs_rename(&lfs, "potato", "coldpotato") => 0;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_rename(&lfs, "coldpotato", "warmpotato") => 0;
    lfs_rename(&lfs, "warmpotato", "hotpotato") => 0;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_remove(&lfs, "potato") => LFS_ERR_NOENT;
    lfs_remove(&lfs, "coldpotato") => LFS_ERR_NOENT;
    lfs_remove(&lfs, "warmpotato") => LFS_ERR_NOENT;
    lfs_remove(&lfs, "hotpotato") => LFS_ERR_NOTEMPTY;
    lfs_unmount(&lfs) => 0;

    // try cross-directory renaming
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "coldpotato") => 0;
    lfs_rename(&lfs, "hotpotato/baked", "coldpotato/baked") => 0;
    lfs_rename(&lfs, "coldpotato", "hotpotato") => LFS_ERR_NOTEMPTY;
    lfs_remove(&lfs, "coldpotato") => LFS_ERR_NOTEMPTY;
    lfs_remove(&lfs, "hotpotato") => LFS_ERR_NOTEMPTY;
    lfs_rename(&lfs, "hotpotato/fried", "coldpotato/fried") => 0;
    lfs_rename(&lfs, "coldpotato", "hotpotato") => LFS_ERR_NOTEMPTY;
    lfs_remove(&lfs, "coldpotato") => LFS_ERR_NOTEMPTY;
    lfs_remove(&lfs, "hotpotato") => LFS_ERR_NOTEMPTY;
    lfs_rename(&lfs, "hotpotato/sweet", "coldpotato/sweet") => 0;
    lfs_rename(&lfs, "coldpotato", "hotpotato") => 0;
    lfs_remove(&lfs, "coldpotato") => LFS_ERR_NOENT;
    lfs_remove(&lfs, "hotpotato") => LFS_ERR_NOTEMPTY;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_open(&lfs, &dir, "hotpotato") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, ".") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "..") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "baked") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "fried") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "sweet") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
    
    // final remove
    lfs_mount(&lfs, cfg) => 0;
    lfs_remove(&lfs, "hotpotato") => LFS_ERR_NOTEMPTY;
    lfs_remove(&lfs, "hotpotato/baked") => 0;
    lfs_remove(&lfs, "hotpotato") => LFS_ERR_NOTEMPTY;
    lfs_remove(&lfs, "hotpotato/fried") => 0;
    lfs_remove(&lfs, "hotpotato") => LFS_ERR_NOTEMPTY;
    lfs_remove(&lfs, "hotpotato/sweet") => 0;
    lfs_remove(&lfs, "hotpotato") => 0;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, ".") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "..") == 0);
    info.type => LFS_TYPE_DIR;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "burito") == 0);
    info.type => LFS_TYPE_REG;
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_recursive_remove]
defines.N = [10, 100]
if = 'N < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "prickly-pear") => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "prickly-pear/cactus%03d", i);
        lfs_mkdir(&lfs, path) => 0;
    }
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "prickly-pear") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "cactus%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_DIR);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs);

    lfs_mount(&lfs, cfg) => 0;
    lfs_remove(&lfs, "prickly-pear") => LFS_ERR_NOTEMPTY;

    lfs_dir_open(&lfs, &dir, "prickly-pear") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "cactus%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_DIR);
        assert(strcmp(info.name, path) == 0);
        sprintf(path, "prickly-pear/%s", info.name);
        lfs_remove(&lfs, path) => 0;
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;

    lfs_remove(&lfs, "prickly-pear") => 0;
    lfs_remove(&lfs, "prickly-pear") => LFS_ERR_NOENT;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_remove(&lfs, "prickly-pear") => LFS_ERR_NOENT;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_remove_read]
defines.N = 10
if = 'N < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "prickly-pear") => 0;
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "prickly-pear/cactus%03d", i);
        lfs_mkdir(&lfs, path) => 0;
    }
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "prickly-pear") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    for (int i = 0; i < N; i++) {
        char path[1024];
        sprintf(path, "cactus%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(info.type == LFS_TYPE_DIR);
        assert(strcmp(info.name, path) == 0);
    }
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs);

    for (lfs_size_t k = 0; k < N; k++) {
        for (lfs_size_t j = 0; j < N; j++) {
            lfs_mount(&lfs, cfg) => 0;
            lfs_dir_open(&lfs, &dir, "prickly-pear") => 0;
            lfs_dir_read(&lfs, &dir, &info) => 1;
            assert(info.type == LFS_TYPE_DIR);
            assert(strcmp(info.name, ".") == 0);
            lfs_dir_read(&lfs, &dir, &info) => 1;
            assert(info.type == LFS_TYPE_DIR);
            assert(strcmp(info.name, "..") == 0);
            // iterate over dirs < j
            for (unsigned i = 0; i < j; i++) {
                char path[1024];
                sprintf(path, "cactus%03d", i);
                lfs_dir_read(&lfs, &dir, &info) => 1;
                assert(info.type == LFS_TYPE_DIR);
                assert(strcmp(info.name, path) == 0);
            }

            // remove k while iterating
            char path[1024];
            sprintf(path, "prickly-pear/cactus%03d", k);
            lfs_remove(&lfs, path) => 0;

            // iterate over dirs >= j
            for (unsigned i = j; i < ((k >= j) ? N-1 : N); i++) {
                char path[1024];
                sprintf(path, "cactus%03d", (k >= j && i >= k) ? i+1 : i);
                lfs_dir_read(&lfs, &dir, &info) => 1;
                assert(info.type == LFS_TYPE_DIR);
                assert(strcmp(info.name, path) == 0);
            }
            lfs_dir_read(&lfs, &dir, &info) => 0;
            lfs_dir_close(&lfs, &dir) => 0;

            // recreate k
            sprintf(path, "prickly-pear/cactus%03d", k);
            lfs_mkdir(&lfs, path) => 0;
            lfs_unmount(&lfs) => 0;
        }
    }
'''

[cases.test_dirs_other_errors]
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "potato") => 0;
    lfs_file_t file;
    lfs_file_open(&lfs, &file, "burito",
            LFS_O_WRONLY | LFS_O_CREAT | LFS_O_EXCL) => 0;
    lfs_file_close(&lfs, &file) => 0;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;

    lfs_mkdir(&lfs, "potato") => LFS_ERR_EXIST;
    lfs_mkdir(&lfs, "burito") => LFS_ERR_EXIST;
    lfs_file_open(&lfs, &file, "burito",
            LFS_O_WRONLY | LFS_O_CREAT | LFS_O_EXCL) => LFS_ERR_EXIST;
    lfs_file_open(&lfs, &file, "potato",
            LFS_O_WRONLY | LFS_O_CREAT | LFS_O_EXCL) => LFS_ERR_EXIST;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "tomato") => LFS_ERR_NOENT;
    lfs_dir_open(&lfs, &dir, "burito") => LFS_ERR_NOTDIR;
    lfs_file_open(&lfs, &file, "tomato", LFS_O_RDONLY) => LFS_ERR_NOENT;
    lfs_file_open(&lfs, &file, "potato", LFS_O_RDONLY) => LFS_ERR_ISDIR;
    lfs_file_open(&lfs, &file, "tomato", LFS_O_WRONLY) => LFS_ERR_NOENT;
    lfs_file_open(&lfs, &file, "potato", LFS_O_WRONLY) => LFS_ERR_ISDIR;
    lfs_file_open(&lfs, &file, "potato",
            LFS_O_WRONLY | LFS_O_CREAT) => LFS_ERR_ISDIR;

    lfs_file_open(&lfs, &file, "tacoto", LFS_O_WRONLY | LFS_O_CREAT) => 0;
    lfs_file_close(&lfs, &file) => 0;
    lfs_rename(&lfs, "tacoto", "potato") => LFS_ERR_ISDIR;
    lfs_rename(&lfs, "potato", "tacoto") => LFS_ERR_NOTDIR;

    lfs_mkdir(&lfs, "/") => LFS_ERR_EXIST;
    lfs_file_open(&lfs, &file, "/",
            LFS_O_WRONLY | LFS_O_CREAT | LFS_O_EXCL) => LFS_ERR_EXIST;
    lfs_file_open(&lfs, &file, "/", LFS_O_RDONLY) => LFS_ERR_ISDIR;
    lfs_file_open(&lfs, &file, "/", LFS_O_WRONLY) => LFS_ERR_ISDIR;
    lfs_file_open(&lfs, &file, "/",
            LFS_O_WRONLY | LFS_O_CREAT) => LFS_ERR_ISDIR;

    // check that errors did not corrupt directory
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_REG);
    assert(strcmp(info.name, "burito") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "potato") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_REG);
    assert(strcmp(info.name, "tacoto") == 0);
    assert(info.size == 0);
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;

    lfs_unmount(&lfs) => 0;

    // or on disk
    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, ".") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "..") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_REG);
    assert(strcmp(info.name, "burito") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_DIR);
    assert(strcmp(info.name, "potato") == 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(info.type == LFS_TYPE_REG);
    assert(strcmp(info.name, "tacoto") == 0);
    assert(info.size == 0);
    lfs_dir_read(&lfs, &dir, &info) => 0;
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_seek]
defines.COUNT = [4, 128, 132]
if = 'COUNT < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "hello") => 0;
    for (int i = 0; i < COUNT; i++) {
        char path[1024];
        sprintf(path, "hello/kitty%03d", i);
        lfs_mkdir(&lfs, path) => 0;
    }
    lfs_unmount(&lfs) => 0;

    // try seeking to each dir entry
    for (int j = 0; j < COUNT; j++) {
        lfs_mount(&lfs, cfg) => 0;
        lfs_dir_t dir;
        lfs_dir_open(&lfs, &dir, "hello") => 0;
        struct lfs_info info;
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, ".") == 0);
        assert(info.type == LFS_TYPE_DIR);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, "..") == 0);
        assert(info.type == LFS_TYPE_DIR);

        for (int i = 0; i < j; i++) {
            char path[1024];
            sprintf(path, "kitty%03d", i);
            lfs_dir_read(&lfs, &dir, &info) => 1;
            assert(strcmp(info.name, path) == 0);
            assert(info.type == LFS_TYPE_DIR);
        }
        lfs_soff_t pos = lfs_dir_tell(&lfs, &dir);
        assert(pos >= 0);

        lfs_dir_seek(&lfs, &dir, pos) => 0;
        char path[1024];
        sprintf(path, "kitty%03d", j);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, path) == 0);
        assert(info.type == LFS_TYPE_DIR);

        lfs_dir_rewind(&lfs, &dir) => 0;
        sprintf(path, "kitty%03u", 0);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, ".") == 0);
        assert(info.type == LFS_TYPE_DIR);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, "..") == 0);
        assert(info.type == LFS_TYPE_DIR);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, path) == 0);
        assert(info.type == LFS_TYPE_DIR);

        lfs_dir_seek(&lfs, &dir, pos) => 0;
        sprintf(path, "kitty%03d", j);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, path) == 0);
        assert(info.type == LFS_TYPE_DIR);

        lfs_dir_close(&lfs, &dir) => 0;
        lfs_unmount(&lfs) => 0;
    }

    // try seeking to end of dir
    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "hello") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, ".") == 0);
    assert(info.type == LFS_TYPE_DIR);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "..") == 0);
    assert(info.type == LFS_TYPE_DIR);

    for (int i = 0; i < COUNT; i++) {
        char path[1024];
        sprintf(path, "kitty%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, path) == 0);
        assert(info.type == LFS_TYPE_DIR);
    }
    lfs_soff_t pos = lfs_dir_tell(&lfs, &dir);
    assert(pos >= 0);

    lfs_dir_read(&lfs, &dir, &info) => 0;

    lfs_dir_seek(&lfs, &dir, pos) => 0;
    lfs_dir_read(&lfs, &dir, &info) => 0;

    lfs_dir_rewind(&lfs, &dir) => 0;
    char path[1024];
    sprintf(path, "kitty%03d", 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, ".") == 0);
    assert(info.type == LFS_TYPE_DIR);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "..") == 0);
    assert(info.type == LFS_TYPE_DIR);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, path) == 0);
    assert(info.type == LFS_TYPE_DIR);

    lfs_dir_seek(&lfs, &dir, pos) => 0;
    lfs_dir_read(&lfs, &dir, &info) => 0;

    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

[cases.test_dirs_toot_seek]
defines.COUNT = [4, 128, 132]
if = 'COUNT < BLOCK_COUNT/2'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    for (int i = 0; i < COUNT; i++) {
        char path[1024];
        sprintf(path, "hi%03d", i);
        lfs_mkdir(&lfs, path) => 0;
    }
    lfs_unmount(&lfs) => 0;

    for (int j = 0; j < COUNT; j++) {
        lfs_mount(&lfs, cfg) => 0;
        lfs_dir_t dir;
        lfs_dir_open(&lfs, &dir, "/") => 0;
        struct lfs_info info;
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, ".") == 0);
        assert(info.type == LFS_TYPE_DIR);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, "..") == 0);
        assert(info.type == LFS_TYPE_DIR);

        for (int i = 0; i < j; i++) {
            char path[1024];
            sprintf(path, "hi%03d", i);
            lfs_dir_read(&lfs, &dir, &info) => 1;
            assert(strcmp(info.name, path) == 0);
            assert(info.type == LFS_TYPE_DIR);
        }
        lfs_soff_t pos = lfs_dir_tell(&lfs, &dir);
        assert(pos >= 0);

        lfs_dir_seek(&lfs, &dir, pos) => 0;
        char path[1024];
        sprintf(path, "hi%03d", j);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, path) == 0);
        assert(info.type == LFS_TYPE_DIR);

        lfs_dir_rewind(&lfs, &dir) => 0;
        sprintf(path, "hi%03u", 0);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, ".") == 0);
        assert(info.type == LFS_TYPE_DIR);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, "..") == 0);
        assert(info.type == LFS_TYPE_DIR);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, path) == 0);
        assert(info.type == LFS_TYPE_DIR);

        lfs_dir_seek(&lfs, &dir, pos) => 0;
        sprintf(path, "hi%03d", j);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, path) == 0);
        assert(info.type == LFS_TYPE_DIR);

        lfs_dir_close(&lfs, &dir) => 0;
        lfs_unmount(&lfs) => 0;
    }

    // try seeking to end of dir
    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "/") => 0;
    struct lfs_info info;
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, ".") == 0);
    assert(info.type == LFS_TYPE_DIR);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "..") == 0);
    assert(info.type == LFS_TYPE_DIR);

    for (int i = 0; i < COUNT; i++) {
        char path[1024];
        sprintf(path, "hi%03d", i);
        lfs_dir_read(&lfs, &dir, &info) => 1;
        assert(strcmp(info.name, path) == 0);
        assert(info.type == LFS_TYPE_DIR);
    }
    lfs_soff_t pos = lfs_dir_tell(&lfs, &dir);
    assert(pos >= 0);

    lfs_dir_read(&lfs, &dir, &info) => 0;

    lfs_dir_seek(&lfs, &dir, pos) => 0;
    lfs_dir_read(&lfs, &dir, &info) => 0;

    lfs_dir_rewind(&lfs, &dir) => 0;
    char path[1024];
    sprintf(path, "hi%03d", 0);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, ".") == 0);
    assert(info.type == LFS_TYPE_DIR);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, "..") == 0);
    assert(info.type == LFS_TYPE_DIR);
    lfs_dir_read(&lfs, &dir, &info) => 1;
    assert(strcmp(info.name, path) == 0);
    assert(info.type == LFS_TYPE_DIR);

    lfs_dir_seek(&lfs, &dir, pos) => 0;
    lfs_dir_read(&lfs, &dir, &info) => 0;

    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
'''

