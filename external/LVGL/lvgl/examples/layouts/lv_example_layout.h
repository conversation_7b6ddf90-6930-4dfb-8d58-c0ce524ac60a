/**
 * @file lv_example_layout.h
 *
 */

#ifndef LV_EXAMPLE_LAYOUT_H
#define LV_EXAMPLE_LAYOUT_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "flex/lv_example_flex.h"
#include "grid/lv_example_grid.h"

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_LAYOUT_H*/
