/**
 * @file lv_example_others.h
 *
 */

#ifndef LV_EXAMPLE_OTHERS_H
#define LV_EXAMPLE_OTHERS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "snapshot/lv_example_snapshot.h"
/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EX_OTHERS_H*/
