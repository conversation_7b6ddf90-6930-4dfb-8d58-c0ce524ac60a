#include "../../lvgl.h"

#if LV_BUILD_EXAMPLES

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_IMG_HAND
#define LV_ATTRIBUTE_IMG_IMG_HAND
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_IMG_IMG_HAND uint8_t img_hand_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x92, 0x04, 0x6e, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0x47, 0x00, 0x57, 0x25, 0x57, 0x49, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x6d, 0x0c, 0x49, 0x74, 0x24, 0xeb, 0x24, 0xf0, 0x49, 0xbc, 0x49, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x03, 0x24, 0x8f, 0x24, 0xfb, 0x00, 0xff, 0x00, 0xff, 0x00, 0xec, 0x24, 0x87, 0x49, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x92, 0x0f, 0x49, 0xcb, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xc4, 0xdb, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0xa0, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x25, 0xf8, 0x24, 0xa8, 0x6e, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x4b, 0x00, 0xff, 0x24, 0xdb, 0x6e, 0x54, 0x6d, 0x50, 0x00, 0xb8, 0x00, 0xff, 0x25, 0xff, 0x49, 0xff, 0x49, 0xff, 0x25, 0xff, 0x49, 0xff, 0x49, 0xff, 0x49, 0xff, 0x49, 0xff, 0x49, 0xfb, 0x49, 0xe8, 0x49, 0xe4, 0x49, 0xdc, 0x49, 0xd7, 0x49, 0xd0, 0x49, 0xb3, 0x49, 0xb0, 0x49, 0xaf, 0x49, 0xab, 0x49, 0xa7, 0x49, 0x87, 0x49, 0x84, 0x49, 0x80, 0x49, 0x7f, 0x49, 0x7b, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x24, 0x57, 0x00, 0x57, 0x00, 0x57, 0x24, 0x57, 0x24, 0x57, 0x24, 0x57, 0x24, 0x57, 0x24, 0x57, 0x24, 0x57, 0x25, 0x9c, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x25, 0xff, 0x49, 0xa0, 0x24, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x57, 0x25, 0x57, 0x25, 0x57, 0x24, 0x57, 0x24, 0x57, 0x25, 0x57, 0x24, 0x57, 0x24, 0x57, 0x24, 0x57, 0x24, 0x57, 0x24, 0x57, 0x24, 0x57, 0x24, 0x57, 0x49, 0x50,
  0x49, 0xb7, 0x00, 0xff, 0x49, 0x8c, 0x6e, 0x04, 0x6e, 0x0f, 0x25, 0x37, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xff, 0x24, 0xff, 0x24, 0xff, 0x24, 0xff, 0x24, 0xff, 0x24, 0xfc, 0x24, 0xfb, 0x24, 0xf8, 0x24, 0xf7, 0x24, 0xf4, 0x24, 0xf3, 0x24, 0xf3, 0x24, 0xf3, 0x00, 0xf0, 0x00, 0xec, 0x00, 0xeb, 0x00, 0xeb, 0x00, 0xeb, 0x00, 0xeb, 0x24, 0xe8,
  0x49, 0x77, 0x00, 0xff, 0x24, 0x8b, 0x92, 0x03, 0x6e, 0x14, 0x49, 0x5c, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xff, 0x24, 0xff, 0x24, 0xff, 0x24, 0xff, 0x24, 0xff, 0x25, 0xff, 0x25, 0xff, 0x25, 0xff, 0x25, 0xff, 0x25, 0xff, 0x49, 0xff, 0x49, 0xff, 0x49, 0xff, 0x49, 0xfc, 0x49, 0xf7, 0x49, 0xb7, 0x49, 0xa8, 0x49, 0xa3, 0x49, 0xa3, 0x49, 0xa8, 0x49, 0x93, 0x49, 0x7f, 0x49, 0x6b, 0x25, 0x5c, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x00, 0x57, 0x24, 0x57, 0x00, 0x57, 0x24, 0x9c, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x49, 0xff, 0x49, 0x90, 0x24, 0x54, 0x25, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x57, 0x49, 0x53, 0x6d, 0x4b, 0x6d, 0x44, 0x6e, 0x37, 0x6e, 0x2f, 0x6e, 0x27, 0x6e, 0x24, 0x6e, 0x24, 0x6e, 0x24, 0x92, 0x17, 0xb7, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x25, 0x30, 0x00, 0xf4, 0x00, 0xff, 0x49, 0xff, 0x49, 0xff, 0x00, 0xff, 0x00, 0xff, 0x49, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0xcc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xf8, 0x24, 0xa8, 0x6d, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x6d, 0x2b, 0x00, 0xf3, 0x00, 0xff, 0x00, 0xff, 0x00, 0xf3, 0x24, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x18, 0x49, 0xd8, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xec, 0x24, 0x87, 0x49, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x92, 0x0c, 0x49, 0x5f, 0x49, 0x8c, 0x92, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0x54, 0x00, 0x57, 0x25, 0x57, 0x49, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x84, 0x04, 0x2c, 0x63, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8a, 0x52, 0x47, 0x20, 0x00, 0x57, 0x04, 0x21, 0x57, 0xe7, 0x39, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xeb, 0x5a, 0x0c, 0xc7, 0x39, 0x74, 0xc3, 0x18, 0xeb, 0xc3, 0x18, 0xf0, 0xc7, 0x39, 0xbc, 0xe8, 0x41, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x82, 0x10, 0x03, 0xe4, 0x20, 0x8f, 0xa3, 0x18, 0xfb, 0x20, 0x00, 0xff, 0x21, 0x08, 0xff, 0x61, 0x08, 0xec, 0x04, 0x21, 0x87, 0x49, 0x4a, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xef, 0x7b, 0x0f, 0xa7, 0x39, 0xcb, 0x20, 0x00, 0xff, 0x21, 0x08, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0xc3, 0x18, 0xc4, 0xd7, 0xbd, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x29, 0xa0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x45, 0x29, 0xf8, 0xa3, 0x18, 0xa8, 0x2d, 0x6b, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x62, 0x10, 0x4b, 0x62, 0x10, 0xff, 0xc3, 0x18, 0xdb, 0x0c, 0x63, 0x54, 0xeb, 0x5a, 0x50, 0x82, 0x10, 0xb8, 0x21, 0x08, 0xff, 0x04, 0x21, 0xff, 0xc7, 0x39, 0xff, 0xe7, 0x39, 0xff, 0x86, 0x31, 0xff, 0xe8, 0x41, 0xff, 0xe8, 0x41, 0xff, 0xe8, 0x41, 0xff, 0x08, 0x42, 0xff, 0x08, 0x42, 0xfb, 0x28, 0x42, 0xe8, 0x28, 0x42, 0xe4, 0x29, 0x4a, 0xdc, 0x29, 0x4a, 0xd7, 0x29, 0x4a, 0xd0, 0x49, 0x4a, 0xb3, 0x49, 0x4a, 0xb0, 0x49, 0x4a, 0xaf, 0x49, 0x4a, 0xab, 0x49, 0x4a, 0xa7, 0x29, 0x4a, 0x87, 0x29, 0x4a, 0x84, 0x28, 0x42, 0x80, 0x28, 0x42, 0x7f, 0x08, 0x42, 0x7b, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x20, 0x00, 0x57, 0x21, 0x08, 0x57, 0x20, 0x00, 0x57, 0x20, 0x00, 0x57, 0x21, 0x08, 0x57, 0x41, 0x08, 0x57, 0x41, 0x08, 0x57, 0x21, 0x08, 0x57, 0x41, 0x08, 0x57, 0x41, 0x08, 0x57, 0x41, 0x08, 0x57, 0x61, 0x08, 0x57, 0x41, 0x08, 0x57, 0x61, 0x08, 0x57, 0x61, 0x08, 0x57, 0x82, 0x10, 0x57, 0x82, 0x10, 0x57, 0x82, 0x10, 0x57, 0x82, 0x10, 0x57, 0x82, 0x10, 0x57, 0xa2, 0x10, 0x57, 0xa3, 0x18, 0x57, 0xa2, 0x10, 0x57, 0xa2, 0x10, 0x57, 0xa3, 0x18, 0x57, 0x86, 0x31, 0x9c, 0x61, 0x08, 0xff, 0x21, 0x08, 0xff, 0x41, 0x08, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x41, 0x08, 0xff, 0x21, 0x08, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xff, 0x25, 0x29, 0xff, 0x08, 0x42, 0xa0, 0xc3, 0x18, 0x57, 0xc7, 0x39, 0x57, 0xe8, 0x41, 0x57, 0x08, 0x42, 0x57, 0xe8, 0x41, 0x57, 0xa7, 0x39, 0x57, 0xa6, 0x31, 0x57, 0xa6, 0x31, 0x57, 0xa7, 0x39, 0x57, 0xa7, 0x39, 0x57, 0x66, 0x31, 0x57, 0x25, 0x29, 0x57, 0x04, 0x21, 0x57, 0x04, 0x21, 0x57, 0x04, 0x21, 0x57, 0x04, 0x21, 0x57, 0xc3, 0x18, 0x57, 0xa2, 0x10, 0x57, 0xa3, 0x18, 0x57, 0xc3, 0x18, 0x57, 0xa2, 0x10, 0x57, 0xa3, 0x18, 0x57, 0x28, 0x42, 0x50,
  0x49, 0x4a, 0xb7, 0x20, 0x00, 0xff, 0xa7, 0x39, 0x8c, 0x8e, 0x73, 0x04, 0x8e, 0x73, 0x0f, 0x04, 0x21, 0x37, 0x82, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x21, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x21, 0x08, 0xff, 0x21, 0x08, 0xff, 0x21, 0x08, 0xff, 0x00, 0x00, 0xff, 0x21, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x21, 0x08, 0xff, 0x61, 0x08, 0xff, 0x82, 0x10, 0xff, 0xa3, 0x18, 0xff, 0xa3, 0x18, 0xff, 0xa2, 0x10, 0xff, 0xa3, 0x18, 0xff, 0xc3, 0x18, 0xff, 0xc3, 0x18, 0xfc, 0xc3, 0x18, 0xfb, 0xc3, 0x18, 0xf8, 0xa3, 0x18, 0xf7, 0xa3, 0x18, 0xf4, 0xa3, 0x18, 0xf3, 0xa2, 0x10, 0xf3, 0xa2, 0x10, 0xf3, 0x82, 0x10, 0xf0, 0x41, 0x08, 0xec, 0x00, 0x00, 0xeb, 0x20, 0x00, 0xeb, 0x21, 0x08, 0xeb, 0x00, 0x00, 0xeb, 0xe3, 0x18, 0xe8,
  0xc7, 0x39, 0x77, 0x82, 0x10, 0xff, 0x04, 0x21, 0x8b, 0x10, 0x84, 0x03, 0x4d, 0x6b, 0x14, 0x6a, 0x52, 0x5c, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x21, 0x08, 0xff, 0x21, 0x08, 0xff, 0x21, 0x08, 0xff, 0x21, 0x08, 0xff, 0x41, 0x08, 0xff, 0x62, 0x10, 0xff, 0xa2, 0x10, 0xff, 0xa2, 0x10, 0xff, 0xa3, 0x18, 0xff, 0xc3, 0x18, 0xff, 0xe3, 0x18, 0xff, 0x25, 0x29, 0xff, 0x25, 0x29, 0xff, 0x25, 0x29, 0xff, 0x25, 0x29, 0xff, 0x25, 0x29, 0xff, 0xc7, 0x39, 0xff, 0xe7, 0x39, 0xff, 0xe8, 0x41, 0xff, 0x08, 0x42, 0xfc, 0x08, 0x42, 0xf7, 0x49, 0x4a, 0xb7, 0x49, 0x4a, 0xa8, 0x49, 0x4a, 0xa3, 0x49, 0x4a, 0xa3, 0x49, 0x4a, 0xa8, 0x49, 0x4a, 0x93, 0x28, 0x42, 0x7f, 0xa7, 0x39, 0x6b, 0x04, 0x21, 0x5c, 0x41, 0x08, 0x57, 0x00, 0x00, 0x57, 0x20, 0x00, 0x57, 0x41, 0x08, 0x57, 0x61, 0x08, 0x57, 0x61, 0x08, 0x57, 0x62, 0x10, 0x57, 0xa2, 0x10, 0x57, 0x82, 0x10, 0x57, 0xe4, 0x20, 0x9c, 0x41, 0x08, 0xff, 0x00, 0x00, 0xff, 0x21, 0x08, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x41, 0x08, 0xff, 0x21, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x41, 0x08, 0xff, 0x86, 0x31, 0xff, 0x29, 0x4a, 0x90, 0x82, 0x10, 0x54, 0x25, 0x29, 0x57, 0xa7, 0x39, 0x57, 0xe8, 0x41, 0x57, 0x08, 0x42, 0x57, 0x49, 0x4a, 0x57, 0x8a, 0x52, 0x53, 0xab, 0x5a, 0x4b, 0xcb, 0x5a, 0x44, 0x0c, 0x63, 0x37, 0x4d, 0x6b, 0x2f, 0x6e, 0x73, 0x27, 0x6e, 0x73, 0x24, 0x6e, 0x73, 0x24, 0x6e, 0x73, 0x24, 0xcf, 0x7b, 0x17, 0x55, 0xad, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x24, 0x21, 0x30, 0x61, 0x08, 0xf4, 0x21, 0x08, 0xff, 0xc7, 0x39, 0xff, 0xa6, 0x31, 0xff, 0x41, 0x08, 0xff, 0x82, 0x10, 0xff, 0xc7, 0x39, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x86, 0x31, 0xcc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x04, 0x21, 0xf8, 0xa2, 0x10, 0xa8, 0xaa, 0x52, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xaa, 0x52, 0x2b, 0x62, 0x10, 0xf3, 0x62, 0x10, 0xff, 0x82, 0x10, 0xff, 0x82, 0x10, 0xf3, 0xc3, 0x18, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcb, 0x5a, 0x18, 0xa6, 0x31, 0xd8, 0x62, 0x10, 0xff, 0x00, 0x00, 0xff, 0x21, 0x08, 0xff, 0x61, 0x08, 0xec, 0xc3, 0x18, 0x87, 0xc7, 0x39, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xef, 0x7b, 0x0c, 0xe8, 0x41, 0x5f, 0x08, 0x42, 0x8c, 0xae, 0x73, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc7, 0x39, 0x54, 0x21, 0x08, 0x57, 0x24, 0x21, 0x57, 0xe7, 0x39, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0x10, 0x04, 0x63, 0x2c, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x8a, 0x47, 0x00, 0x20, 0x57, 0x21, 0x04, 0x57, 0x39, 0xe7, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x5a, 0xeb, 0x0c, 0x39, 0xc7, 0x74, 0x18, 0xc3, 0xeb, 0x18, 0xc3, 0xf0, 0x39, 0xc7, 0xbc, 0x41, 0xe8, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x82, 0x03, 0x20, 0xe4, 0x8f, 0x18, 0xa3, 0xfb, 0x00, 0x20, 0xff, 0x08, 0x21, 0xff, 0x08, 0x61, 0xec, 0x21, 0x04, 0x87, 0x4a, 0x49, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7b, 0xef, 0x0f, 0x39, 0xa7, 0xcb, 0x00, 0x20, 0xff, 0x08, 0x21, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x18, 0xc3, 0xc4, 0xbd, 0xd7, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x45, 0xa0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x29, 0x45, 0xf8, 0x18, 0xa3, 0xa8, 0x6b, 0x2d, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x10, 0x62, 0x4b, 0x10, 0x62, 0xff, 0x18, 0xc3, 0xdb, 0x63, 0x0c, 0x54, 0x5a, 0xeb, 0x50, 0x10, 0x82, 0xb8, 0x08, 0x21, 0xff, 0x21, 0x04, 0xff, 0x39, 0xc7, 0xff, 0x39, 0xe7, 0xff, 0x31, 0x86, 0xff, 0x41, 0xe8, 0xff, 0x41, 0xe8, 0xff, 0x41, 0xe8, 0xff, 0x42, 0x08, 0xff, 0x42, 0x08, 0xfb, 0x42, 0x28, 0xe8, 0x42, 0x28, 0xe4, 0x4a, 0x29, 0xdc, 0x4a, 0x29, 0xd7, 0x4a, 0x29, 0xd0, 0x4a, 0x49, 0xb3, 0x4a, 0x49, 0xb0, 0x4a, 0x49, 0xaf, 0x4a, 0x49, 0xab, 0x4a, 0x49, 0xa7, 0x4a, 0x29, 0x87, 0x4a, 0x29, 0x84, 0x42, 0x28, 0x80, 0x42, 0x28, 0x7f, 0x42, 0x08, 0x7b, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x00, 0x57, 0x00, 0x20, 0x57, 0x08, 0x21, 0x57, 0x00, 0x20, 0x57, 0x00, 0x20, 0x57, 0x08, 0x21, 0x57, 0x08, 0x41, 0x57, 0x08, 0x41, 0x57, 0x08, 0x21, 0x57, 0x08, 0x41, 0x57, 0x08, 0x41, 0x57, 0x08, 0x41, 0x57, 0x08, 0x61, 0x57, 0x08, 0x41, 0x57, 0x08, 0x61, 0x57, 0x08, 0x61, 0x57, 0x10, 0x82, 0x57, 0x10, 0x82, 0x57, 0x10, 0x82, 0x57, 0x10, 0x82, 0x57, 0x10, 0x82, 0x57, 0x10, 0xa2, 0x57, 0x18, 0xa3, 0x57, 0x10, 0xa2, 0x57, 0x10, 0xa2, 0x57, 0x18, 0xa3, 0x57, 0x31, 0x86, 0x9c, 0x08, 0x61, 0xff, 0x08, 0x21, 0xff, 0x08, 0x41, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x08, 0x41, 0xff, 0x08, 0x21, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xff, 0x29, 0x25, 0xff, 0x42, 0x08, 0xa0, 0x18, 0xc3, 0x57, 0x39, 0xc7, 0x57, 0x41, 0xe8, 0x57, 0x42, 0x08, 0x57, 0x41, 0xe8, 0x57, 0x39, 0xa7, 0x57, 0x31, 0xa6, 0x57, 0x31, 0xa6, 0x57, 0x39, 0xa7, 0x57, 0x39, 0xa7, 0x57, 0x31, 0x66, 0x57, 0x29, 0x25, 0x57, 0x21, 0x04, 0x57, 0x21, 0x04, 0x57, 0x21, 0x04, 0x57, 0x21, 0x04, 0x57, 0x18, 0xc3, 0x57, 0x10, 0xa2, 0x57, 0x18, 0xa3, 0x57, 0x18, 0xc3, 0x57, 0x10, 0xa2, 0x57, 0x18, 0xa3, 0x57, 0x42, 0x28, 0x50,
  0x4a, 0x49, 0xb7, 0x00, 0x20, 0xff, 0x39, 0xa7, 0x8c, 0x73, 0x8e, 0x04, 0x73, 0x8e, 0x0f, 0x21, 0x04, 0x37, 0x10, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x21, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x08, 0x21, 0xff, 0x08, 0x21, 0xff, 0x08, 0x21, 0xff, 0x00, 0x00, 0xff, 0x08, 0x21, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x21, 0xff, 0x08, 0x61, 0xff, 0x10, 0x82, 0xff, 0x18, 0xa3, 0xff, 0x18, 0xa3, 0xff, 0x10, 0xa2, 0xff, 0x18, 0xa3, 0xff, 0x18, 0xc3, 0xff, 0x18, 0xc3, 0xfc, 0x18, 0xc3, 0xfb, 0x18, 0xc3, 0xf8, 0x18, 0xa3, 0xf7, 0x18, 0xa3, 0xf4, 0x18, 0xa3, 0xf3, 0x10, 0xa2, 0xf3, 0x10, 0xa2, 0xf3, 0x10, 0x82, 0xf0, 0x08, 0x41, 0xec, 0x00, 0x00, 0xeb, 0x00, 0x20, 0xeb, 0x08, 0x21, 0xeb, 0x00, 0x00, 0xeb, 0x18, 0xe3, 0xe8,
  0x39, 0xc7, 0x77, 0x10, 0x82, 0xff, 0x21, 0x04, 0x8b, 0x84, 0x10, 0x03, 0x6b, 0x4d, 0x14, 0x52, 0x6a, 0x5c, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x21, 0xff, 0x08, 0x21, 0xff, 0x08, 0x21, 0xff, 0x08, 0x21, 0xff, 0x08, 0x41, 0xff, 0x10, 0x62, 0xff, 0x10, 0xa2, 0xff, 0x10, 0xa2, 0xff, 0x18, 0xa3, 0xff, 0x18, 0xc3, 0xff, 0x18, 0xe3, 0xff, 0x29, 0x25, 0xff, 0x29, 0x25, 0xff, 0x29, 0x25, 0xff, 0x29, 0x25, 0xff, 0x29, 0x25, 0xff, 0x39, 0xc7, 0xff, 0x39, 0xe7, 0xff, 0x41, 0xe8, 0xff, 0x42, 0x08, 0xfc, 0x42, 0x08, 0xf7, 0x4a, 0x49, 0xb7, 0x4a, 0x49, 0xa8, 0x4a, 0x49, 0xa3, 0x4a, 0x49, 0xa3, 0x4a, 0x49, 0xa8, 0x4a, 0x49, 0x93, 0x42, 0x28, 0x7f, 0x39, 0xa7, 0x6b, 0x21, 0x04, 0x5c, 0x08, 0x41, 0x57, 0x00, 0x00, 0x57, 0x00, 0x20, 0x57, 0x08, 0x41, 0x57, 0x08, 0x61, 0x57, 0x08, 0x61, 0x57, 0x10, 0x62, 0x57, 0x10, 0xa2, 0x57, 0x10, 0x82, 0x57, 0x20, 0xe4, 0x9c, 0x08, 0x41, 0xff, 0x00, 0x00, 0xff, 0x08, 0x21, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0x08, 0x41, 0xff, 0x08, 0x21, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x41, 0xff, 0x31, 0x86, 0xff, 0x4a, 0x29, 0x90, 0x10, 0x82, 0x54, 0x29, 0x25, 0x57, 0x39, 0xa7, 0x57, 0x41, 0xe8, 0x57, 0x42, 0x08, 0x57, 0x4a, 0x49, 0x57, 0x52, 0x8a, 0x53, 0x5a, 0xab, 0x4b, 0x5a, 0xcb, 0x44, 0x63, 0x0c, 0x37, 0x6b, 0x4d, 0x2f, 0x73, 0x6e, 0x27, 0x73, 0x6e, 0x24, 0x73, 0x6e, 0x24, 0x73, 0x6e, 0x24, 0x7b, 0xcf, 0x17, 0xad, 0x55, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x21, 0x24, 0x30, 0x08, 0x61, 0xf4, 0x08, 0x21, 0xff, 0x39, 0xc7, 0xff, 0x31, 0xa6, 0xff, 0x08, 0x41, 0xff, 0x10, 0x82, 0xff, 0x39, 0xc7, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x86, 0xcc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x21, 0x04, 0xf8, 0x10, 0xa2, 0xa8, 0x52, 0xaa, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x52, 0xaa, 0x2b, 0x10, 0x62, 0xf3, 0x10, 0x62, 0xff, 0x10, 0x82, 0xff, 0x10, 0x82, 0xf3, 0x18, 0xc3, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5a, 0xcb, 0x18, 0x31, 0xa6, 0xd8, 0x10, 0x62, 0xff, 0x00, 0x00, 0xff, 0x08, 0x21, 0xff, 0x08, 0x61, 0xec, 0x18, 0xc3, 0x87, 0x39, 0xc7, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0xef, 0x0c, 0x41, 0xe8, 0x5f, 0x42, 0x08, 0x8c, 0x73, 0xae, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xc7, 0x54, 0x08, 0x21, 0x57, 0x21, 0x24, 0x57, 0x39, 0xe7, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
#endif
#if LV_COLOR_DEPTH == 32
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x80, 0x80, 0x04, 0x63, 0x63, 0x63, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4f, 0x4f, 0x4f, 0x47, 0x03, 0x03, 0x03, 0x57, 0x22, 0x22, 0x22, 0x57, 0x3c, 0x3c, 0x3c, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x5b, 0x5b, 0x5b, 0x0c, 0x38, 0x38, 0x38, 0x74, 0x19, 0x19, 0x19, 0xeb, 0x18, 0x18, 0x18, 0xf0, 0x3a, 0x3a, 0x3a, 0xbc, 0x3e, 0x3e, 0x3e, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 0x03, 0x1e, 0x1e, 0x1e, 0x8f, 0x16, 0x16, 0x16, 0xfb, 0x04, 0x04, 0x04, 0xff, 0x06, 0x06, 0x06, 0xff, 0x0b, 0x0b, 0x0b, 0xec, 0x20, 0x20, 0x20, 0x87, 0x4a, 0x4a, 0x4a, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7b, 0x7b, 0x7b, 0x0f, 0x35, 0x35, 0x35, 0xcb, 0x03, 0x03, 0x03, 0xff, 0x06, 0x06, 0x06, 0xff, 0x04, 0x04, 0x04, 0xff, 0x03, 0x03, 0x03, 0xff, 0x1a, 0x1a, 0x1a, 0xc4, 0xb9, 0xb9, 0xb9, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x27, 0x27, 0x27, 0xa0, 0x02, 0x02, 0x02, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x02, 0x02, 0x02, 0xff, 0x00, 0x00, 0x00, 0xff, 0x29, 0x29, 0x29, 0xf8, 0x16, 0x16, 0x16, 0xa8, 0x65, 0x65, 0x65, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x0d, 0x0d, 0x0d, 0x4b, 0x0d, 0x0d, 0x0d, 0xff, 0x17, 0x17, 0x17, 0xdb, 0x62, 0x62, 0x62, 0x54, 0x5c, 0x5c, 0x5c, 0x50, 0x0f, 0x0f, 0x0f, 0xb8, 0x05, 0x05, 0x05, 0xff, 0x22, 0x22, 0x22, 0xff, 0x39, 0x39, 0x39, 0xff, 0x3b, 0x3b, 0x3b, 0xff, 0x2f, 0x2f, 0x2f, 0xff, 0x3d, 0x3d, 0x3d, 0xff, 0x3d, 0x3d, 0x3d, 0xff, 0x3e, 0x3e, 0x3e, 0xff, 0x3f, 0x3f, 0x3f, 0xff, 0x40, 0x40, 0x40, 0xfb, 0x43, 0x43, 0x43, 0xe8, 0x44, 0x44, 0x44, 0xe4, 0x45, 0x45, 0x45, 0xdc, 0x46, 0x46, 0x46, 0xd7, 0x46, 0x46, 0x46, 0xd0, 0x49, 0x49, 0x49, 0xb3, 0x49, 0x49, 0x49, 0xb0, 0x49, 0x49, 0x49, 0xaf, 0x49, 0x49, 0x49, 0xab, 0x49, 0x49, 0x49, 0xa7, 0x46, 0x46, 0x46, 0x87, 0x45, 0x45, 0x45, 0x84, 0x44, 0x44, 0x44, 0x80, 0x43, 0x43, 0x43, 0x7f, 0x42, 0x42, 0x42, 0x7b, 0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x57, 0x01, 0x01, 0x01, 0x57, 0x02, 0x02, 0x02, 0x57, 0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x57, 0x02, 0x02, 0x02, 0x57, 0x04, 0x04, 0x04, 0x57, 0x06, 0x06, 0x06, 0x57, 0x04, 0x04, 0x04, 0x57, 0x04, 0x04, 0x04, 0x57, 0x05, 0x05, 0x05, 0x57, 0x07, 0x07, 0x07, 0x57, 0x08, 0x08, 0x08, 0x57, 0x06, 0x06, 0x06, 0x57, 0x07, 0x07, 0x07, 0x57, 0x08, 0x08, 0x08, 0x57, 0x0a, 0x0a, 0x0a, 0x57, 0x0c, 0x0c, 0x0c, 0x57, 0x0a, 0x0a, 0x0a, 0x57, 0x0b, 0x0b, 0x0b, 0x57, 0x0c, 0x0c, 0x0c, 0x57, 0x0f, 0x0f, 0x0f, 0x57, 0x11, 0x11, 0x11, 0x57, 0x10, 0x10, 0x10, 0x57, 0x10, 0x10, 0x10, 0x57, 0x12, 0x12, 0x12, 0x57, 0x14, 0x14, 0x14, 0x57, 0x16, 0x16, 0x16, 0x57, 0x13, 0x13, 0x13, 0x57, 0x14, 0x14, 0x14, 0x57, 0x15, 0x15, 0x15, 0x57, 0x30, 0x30, 0x30, 0x9c, 0x0c, 0x0c, 0x0c, 0xff, 0x06, 0x06, 0x06, 0xff, 0x0a, 0x0a, 0x0a, 0xff, 0x04, 0x04, 0x04, 0xff, 0x04, 0x04, 0x04, 0xff, 0x09, 0x09, 0x09, 0xff, 0x05, 0x05, 0x05, 0xff, 0x03, 0x03, 0x03, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x03, 0x03, 0xff, 0x25, 0x25, 0x25, 0xff, 0x40, 0x40, 0x40, 0xa0, 0x18, 0x18, 0x18, 0x57, 0x37, 0x37, 0x37, 0x57, 0x3e, 0x3e, 0x3e, 0x57, 0x41, 0x41, 0x41, 0x57, 0x3e, 0x3e, 0x3e, 0x57, 0x35, 0x35, 0x35, 0x57, 0x34, 0x34, 0x34, 0x57, 0x33, 0x33, 0x33, 0x57, 0x35, 0x35, 0x35, 0x57, 0x35, 0x35, 0x35, 0x57, 0x2d, 0x2d, 0x2d, 0x57, 0x26, 0x26, 0x26, 0x57, 0x20, 0x20, 0x20, 0x57, 0x20, 0x20, 0x20, 0x57, 0x22, 0x22, 0x22, 0x57, 0x1f, 0x1f, 0x1f, 0x57, 0x19, 0x19, 0x19, 0x57, 0x14, 0x14, 0x14, 0x57, 0x15, 0x15, 0x15, 0x57, 0x18, 0x18, 0x18, 0x57, 0x14, 0x14, 0x14, 0x57, 0x16, 0x16, 0x16, 0x57, 0x44, 0x44, 0x44, 0x50,
  0x49, 0x49, 0x49, 0xb7, 0x04, 0x04, 0x04, 0xff, 0x35, 0x35, 0x35, 0x8c, 0x6f, 0x6f, 0x6f, 0x04, 0x6f, 0x6f, 0x6f, 0x0f, 0x21, 0x21, 0x21, 0x37, 0x0f, 0x0f, 0x0f, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x05, 0x05, 0x05, 0xff, 0x02, 0x02, 0x02, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x03, 0x03, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x02, 0x02, 0x02, 0xff, 0x02, 0x02, 0x02, 0xff, 0x03, 0x03, 0x03, 0xff, 0x04, 0x04, 0x04, 0xff, 0x02, 0x02, 0x02, 0xff, 0x02, 0x02, 0x02, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x05, 0x05, 0x05, 0xff, 0x05, 0x05, 0x05, 0xff, 0x05, 0x05, 0x05, 0xff, 0x02, 0x02, 0x02, 0xff, 0x06, 0x06, 0x06, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x05, 0x05, 0x05, 0xff, 0x0c, 0x0c, 0x0c, 0xff, 0x10, 0x10, 0x10, 0xff, 0x16, 0x16, 0x16, 0xff, 0x15, 0x15, 0x15, 0xff, 0x14, 0x14, 0x14, 0xff, 0x16, 0x16, 0x16, 0xff, 0x18, 0x18, 0x18, 0xff, 0x18, 0x18, 0x18, 0xfc, 0x18, 0x18, 0x18, 0xfb, 0x17, 0x17, 0x17, 0xf8, 0x16, 0x16, 0x16, 0xf7, 0x16, 0x16, 0x16, 0xf4, 0x15, 0x15, 0x15, 0xf3, 0x14, 0x14, 0x14, 0xf3, 0x13, 0x13, 0x13, 0xf3, 0x10, 0x10, 0x10, 0xf0, 0x07, 0x07, 0x07, 0xec, 0x02, 0x02, 0x02, 0xeb, 0x03, 0x03, 0x03, 0xeb, 0x05, 0x05, 0x05, 0xeb, 0x02, 0x02, 0x02, 0xeb, 0x1b, 0x1b, 0x1b, 0xe8,
  0x38, 0x38, 0x38, 0x77, 0x0f, 0x0f, 0x0f, 0xff, 0x20, 0x20, 0x20, 0x8b, 0x80, 0x80, 0x80, 0x03, 0x6a, 0x6a, 0x6a, 0x14, 0x4e, 0x4e, 0x4e, 0x5c, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x04, 0x04, 0x04, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x02, 0x02, 0x02, 0xff, 0x06, 0x06, 0x06, 0xff, 0x06, 0x06, 0x06, 0xff, 0x05, 0x05, 0x05, 0xff, 0x06, 0x06, 0x06, 0xff, 0x09, 0x09, 0x09, 0xff, 0x0d, 0x0d, 0x0d, 0xff, 0x14, 0x14, 0x14, 0xff, 0x14, 0x14, 0x14, 0xff, 0x15, 0x15, 0x15, 0xff, 0x19, 0x19, 0x19, 0xff, 0x1c, 0x1c, 0x1c, 0xff, 0x26, 0x26, 0x26, 0xff, 0x26, 0x26, 0x26, 0xff, 0x26, 0x26, 0x26, 0xff, 0x26, 0x26, 0x26, 0xff, 0x25, 0x25, 0x25, 0xff, 0x37, 0x37, 0x37, 0xff, 0x3b, 0x3b, 0x3b, 0xff, 0x3e, 0x3e, 0x3e, 0xff, 0x3f, 0x3f, 0x3f, 0xfc, 0x41, 0x41, 0x41, 0xf7, 0x48, 0x48, 0x48, 0xb7, 0x49, 0x49, 0x49, 0xa8, 0x49, 0x49, 0x49, 0xa3, 0x49, 0x49, 0x49, 0xa3, 0x49, 0x49, 0x49, 0xa8, 0x48, 0x48, 0x48, 0x93, 0x43, 0x43, 0x43, 0x7f, 0x36, 0x36, 0x36, 0x6b, 0x22, 0x22, 0x22, 0x5c, 0x0a, 0x0a, 0x0a, 0x57, 0x01, 0x01, 0x01, 0x57, 0x03, 0x03, 0x03, 0x57, 0x09, 0x09, 0x09, 0x57, 0x0c, 0x0c, 0x0c, 0x57, 0x0c, 0x0c, 0x0c, 0x57, 0x0d, 0x0d, 0x0d, 0x57, 0x13, 0x13, 0x13, 0x57, 0x0f, 0x0f, 0x0f, 0x57, 0x1e, 0x1e, 0x1e, 0x9c, 0x07, 0x07, 0x07, 0xff, 0x00, 0x00, 0x00, 0xff, 0x05, 0x05, 0x05, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x07, 0x07, 0x07, 0xff, 0x05, 0x05, 0x05, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x00, 0x00, 0x00, 0xff, 0x07, 0x07, 0x07, 0xff, 0x31, 0x31, 0x31, 0xff, 0x45, 0x45, 0x45, 0x90, 0x12, 0x12, 0x12, 0x54, 0x25, 0x25, 0x25, 0x57, 0x36, 0x36, 0x36, 0x57, 0x3d, 0x3d, 0x3d, 0x57, 0x3f, 0x3f, 0x3f, 0x57, 0x47, 0x47, 0x47, 0x57, 0x50, 0x50, 0x50, 0x53, 0x55, 0x55, 0x55, 0x4b, 0x59, 0x59, 0x59, 0x44, 0x62, 0x62, 0x62, 0x37, 0x67, 0x67, 0x67, 0x2f, 0x6d, 0x6d, 0x6d, 0x27, 0x6e, 0x6e, 0x6e, 0x24, 0x6e, 0x6e, 0x6e, 0x24, 0x6e, 0x6e, 0x6e, 0x24, 0x79, 0x79, 0x79, 0x17, 0xa7, 0xa7, 0xa7, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x23, 0x23, 0x23, 0x30, 0x0b, 0x0b, 0x0b, 0xf4, 0x05, 0x05, 0x05, 0xff, 0x38, 0x38, 0x38, 0xff, 0x33, 0x33, 0x33, 0xff, 0x07, 0x07, 0x07, 0xff, 0x10, 0x10, 0x10, 0xff, 0x37, 0x37, 0x37, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, 0x2f, 0x2f, 0xcc, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x20, 0x20, 0x20, 0xf8, 0x14, 0x14, 0x14, 0xa8, 0x53, 0x53, 0x53, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x53, 0x53, 0x53, 0x2b, 0x0d, 0x0d, 0x0d, 0xf3, 0x0d, 0x0d, 0x0d, 0xff, 0x10, 0x10, 0x10, 0xff, 0x10, 0x10, 0x10, 0xf3, 0x17, 0x17, 0x17, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5a, 0x5a, 0x5a, 0x18, 0x34, 0x34, 0x34, 0xd8, 0x0d, 0x0d, 0x0d, 0xff, 0x02, 0x02, 0x02, 0xff, 0x05, 0x05, 0x05, 0xff, 0x0b, 0x0b, 0x0b, 0xec, 0x19, 0x19, 0x19, 0x87, 0x38, 0x38, 0x38, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x7b, 0x7b, 0x0c, 0x3e, 0x3e, 0x3e, 0x5f, 0x41, 0x41, 0x41, 0x8c, 0x74, 0x74, 0x74, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0x39, 0x39, 0x54, 0x05, 0x05, 0x05, 0x57, 0x24, 0x24, 0x24, 0x57, 0x3c, 0x3c, 0x3c, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
#endif
};

const lv_img_dsc_t img_hand = {
  .header.always_zero = 0,
  .header.w = 100,
  .header.h = 9,
  .data_size = 900 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = img_hand_map,
};

#endif /* LV_BUILD_EXAMPLES */
