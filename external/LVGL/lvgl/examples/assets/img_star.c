#include "../../lvgl.h"

#if LV_BUILD_EXAMPLES

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_IMG_STAR
#define LV_ATTRIBUTE_IMG_IMG_STAR
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG_IMG_STAR uint8_t img_star_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Blue: 2 bit, Green: 3 bit, Red: 3 bit, Alpha 8 bit */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x54, 0xfd, 0x23, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0xc7, 0xfd, 0xac, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0xf8, 0x50, 0xf8, 0xec, 0xfd, 0xff, 0xfd, 0x2c, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0xf8, 0x03, 0xf8, 0xaf, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0xf8, 0x4b, 0xf8, 0xe8, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0x34, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0xf8, 0x08, 0xf8, 0x9c, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xb7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0xf8, 0x3f, 0xf8, 0xe8, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xf0, 0xfd, 0x3f, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0xf8, 0x0b, 0xf8, 0x8f, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x2c, 0xf8, 0xef, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xe4, 0xfd, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x07, 0xfe, 0x2f, 0xfe, 0x5b, 0xfd, 0xb0, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xc4, 0xfe, 0x5b, 0xfe, 0x34, 0xfe, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x00,
  0xfd, 0x08, 0xfe, 0x18, 0xfe, 0x3f, 0xfe, 0x68, 0xfe, 0x93, 0xfe, 0xbc, 0xfe, 0xe7, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfc, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xeb, 0xfe, 0xc3, 0xfe, 0x98, 0xfe, 0x6f, 0xfe, 0x44, 0xfe, 0x1c, 0xfd, 0x07,
  0xfd, 0x87, 0xfd, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xfd, 0x83,
  0x00, 0x00, 0xf8, 0x93, 0xf8, 0xfb, 0xfd, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xf8, 0xfb, 0xf8, 0x8f, 0xf8, 0x0c,
  0x00, 0x00, 0xf8, 0x03, 0xf8, 0x8b, 0xf8, 0xfb, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xfc, 0xf8, 0x8c, 0xf8, 0x0b, 0x00, 0x00,
  0xf8, 0x00, 0x00, 0x00, 0xf8, 0x08, 0xf8, 0x87, 0xf8, 0xf8, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0x88, 0xf8, 0x08, 0x00, 0x00, 0xf8, 0x00,
  0xf8, 0x00, 0xf8, 0x00, 0x00, 0x00, 0xf8, 0x0b, 0xf8, 0x84, 0xf8, 0xfb, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0x7f, 0xf8, 0x07, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xf8, 0x00, 0xf8, 0x00, 0x00, 0x00, 0xf8, 0x07, 0xf8, 0x84, 0xf8, 0xfc, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0x73, 0xf8, 0x07, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0xf8, 0x00, 0x00, 0x00, 0xf8, 0x03, 0xf8, 0x83, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0x6c, 0xf8, 0x07, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xb4, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0x8b, 0xfc, 0x0b, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0xc8, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0x97, 0xfd, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0xef, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xc0, 0xfd, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0x20, 0xfd, 0xf4, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xe8, 0xfd, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0x4b, 0xfd, 0xf7, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0x1f, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0x77, 0xfd, 0xfb, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xef, 0xf8, 0xf8, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0x47, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x00, 0xfd, 0xa3, 0xfd, 0xfc, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xfc, 0xfd, 0xb3, 0xfd, 0x58, 0xf8, 0x5c, 0xf8, 0xb0, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0x74, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0xcb, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xc0, 0xfd, 0x5b, 0xfd, 0x17, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x1b, 0xf8, 0x58, 0xf8, 0xbf, 0xf8, 0xff, 0xf8, 0xff, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xa4, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0x0b, 0xfd, 0xdc, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xdb, 0xfd, 0x5c, 0xfd, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x1b, 0xf8, 0x5c, 0xf8, 0xd7, 0xf8, 0xff, 0xfd, 0xff, 0xfd, 0xd3, 0xfd, 0x00, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x30, 0xfd, 0xef, 0xfd, 0xf0, 0xfd, 0x6b, 0xfd, 0x10, 0xfd, 0x03, 0x00, 0x00, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x03, 0xf8, 0x14, 0xf8, 0x6f, 0xf8, 0xef, 0xfd, 0xff, 0xfd, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfd, 0x4b, 0xfd, 0x70, 0xfd, 0x10, 0xfd, 0x03, 0x00, 0x00, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0xf8, 0x10, 0xf8, 0x90, 0xfc, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Blue: 5 bit, Green: 6 bit, Red: 5 bit, Alpha 8 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0xfe, 0x54, 0xc8, 0xfe, 0x23, 0x00, 0x00, 0x00, 0xea, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0xfe, 0xc7, 0xe9, 0xfe, 0xac, 0xa6, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x50, 0x61, 0xfe, 0xec, 0xea, 0xfe, 0xff, 0xea, 0xfe, 0x2c, 0x00, 0x00, 0x00, 0xea, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x03, 0x60, 0xfe, 0xaf, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xea, 0xfe, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x4b, 0x60, 0xfe, 0xe8, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0x34, 0x00, 0x00, 0x00, 0xea, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x08, 0x60, 0xfe, 0x9c, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xb7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x3f, 0x60, 0xfe, 0xe8, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xf0, 0xea, 0xfe, 0x3f, 0x00, 0x00, 0x00, 0xea, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x0b, 0x60, 0xfe, 0x8f, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0xfe, 0x2c, 0x60, 0xfe, 0xef, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xe4, 0xea, 0xfe, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xff, 0x07, 0x30, 0xff, 0x2f, 0x2f, 0xff, 0x5b, 0xc7, 0xfe, 0xb0, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0x0c, 0xff, 0xc4, 0x51, 0xff, 0x5b, 0x30, 0xff, 0x34, 0x30, 0xff, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xff, 0x00,
  0xc6, 0xfe, 0x08, 0x0d, 0xff, 0x18, 0x30, 0xff, 0x3f, 0x30, 0xff, 0x68, 0x30, 0xff, 0x93, 0x30, 0xff, 0xbc, 0x30, 0xff, 0xe7, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x2e, 0xff, 0xff, 0x84, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0x2f, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xeb, 0x30, 0xff, 0xc3, 0x30, 0xff, 0x98, 0x30, 0xff, 0x6f, 0x30, 0xff, 0x44, 0x2e, 0xff, 0x1c, 0xc7, 0xfe, 0x07,
  0xa6, 0xfe, 0x87, 0xea, 0xfe, 0xff, 0x2e, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x50, 0xff, 0xff, 0x0c, 0xff, 0xff, 0x61, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0x0e, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x0e, 0xff, 0xff, 0xea, 0xfe, 0xff, 0xc7, 0xfe, 0x83,
  0x00, 0x00, 0x00, 0x40, 0xfe, 0x93, 0x60, 0xfe, 0xfb, 0xa4, 0xfe, 0xff, 0xe9, 0xfe, 0xff, 0x0d, 0xff, 0xff, 0x2e, 0xff, 0xff, 0x30, 0xff, 0xff, 0x50, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0xc8, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0x0d, 0xff, 0xff, 0x50, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x50, 0xff, 0xff, 0x30, 0xff, 0xff, 0x2e, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xea, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0x60, 0xfe, 0xfb, 0x40, 0xfe, 0x8f, 0x00, 0xfe, 0x0c,
  0x00, 0x00, 0x00, 0x60, 0xfe, 0x03, 0x60, 0xfe, 0x8b, 0x60, 0xfe, 0xfb, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xc9, 0xfe, 0xff, 0x0c, 0xff, 0xff, 0x2e, 0xff, 0xff, 0x30, 0xff, 0xff, 0x51, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xa5, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0x0c, 0xff, 0xff, 0x50, 0xff, 0xff, 0x50, 0xff, 0xff, 0x50, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x0c, 0xff, 0xff, 0xe9, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0x40, 0xfe, 0xff, 0x60, 0xfe, 0xfc, 0x60, 0xfe, 0x8c, 0x60, 0xfe, 0x0b, 0x00, 0x00, 0x00,
  0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x08, 0x60, 0xfe, 0x87, 0x60, 0xfe, 0xf8, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x40, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xa4, 0xfe, 0xff, 0xc7, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0x2f, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x83, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0x30, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x0c, 0xff, 0xff, 0xc8, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0x81, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0x88, 0x60, 0xfe, 0x08, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00,
  0x60, 0xfe, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x0b, 0x60, 0xfe, 0x84, 0x60, 0xfe, 0xfb, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0x83, 0xfe, 0xff, 0xc6, 0xfe, 0xff, 0xa6, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xc7, 0xfe, 0xff, 0x84, 0xfe, 0xff, 0x82, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0x7f, 0x60, 0xfe, 0x07, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x07, 0x60, 0xfe, 0x84, 0x60, 0xfe, 0xfc, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x82, 0xfe, 0xff, 0x83, 0xfe, 0xff, 0xc7, 0xfe, 0xff, 0xa6, 0xfe, 0xff, 0xc7, 0xfe, 0xff, 0x84, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0x73, 0x60, 0xfe, 0x07, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x03, 0x60, 0xfe, 0x83, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x82, 0xfe, 0xff, 0x84, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xa7, 0xfe, 0xff, 0xe9, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xc9, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xe9, 0xfe, 0xff, 0xc8, 0xfe, 0xff, 0xa4, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0x6c, 0x60, 0xfe, 0x07, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0xfe, 0xb4, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xa6, 0xfe, 0xff, 0xe9, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0x81, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xc7, 0xfe, 0x8b, 0x83, 0xfe, 0x0b, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0xc8, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xc8, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x84, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0x97, 0xeb, 0xfe, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0xef, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xc7, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0xa7, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xc0, 0xeb, 0xfe, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0x20, 0xa5, 0xfe, 0xf4, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xa6, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x81, 0xfe, 0xff, 0xc9, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xe8, 0xeb, 0xfe, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0x4b, 0xa5, 0xfe, 0xf7, 0xa5, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xc9, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x83, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0x1f, 0xe9, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0x77, 0xa5, 0xfe, 0xfb, 0xa5, 0xfe, 0xff, 0xc8, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xef, 0x61, 0xfe, 0xf8, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0xa5, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0x47, 0xea, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0xa3, 0xa5, 0xfe, 0xfc, 0xa6, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xfc, 0xeb, 0xfe, 0xb3, 0xea, 0xfe, 0x58, 0x60, 0xfe, 0x5c, 0x60, 0xfe, 0xb0, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x61, 0xfe, 0xff, 0xc8, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0x74, 0xea, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0xcb, 0xa6, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xc0, 0xea, 0xfe, 0x5b, 0xea, 0xfe, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x1b, 0x60, 0xfe, 0x58, 0x60, 0xfe, 0xbf, 0x60, 0xfe, 0xff, 0x60, 0xfe, 0xff, 0x82, 0xfe, 0xff, 0xea, 0xfe, 0xff, 0xeb, 0xfe, 0xa4, 0xea, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0x0b, 0xa5, 0xfe, 0xdc, 0xc9, 0xfe, 0xff, 0xeb, 0xfe, 0xff, 0xea, 0xfe, 0xdb, 0xea, 0xfe, 0x5c, 0xea, 0xfe, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x1b, 0x60, 0xfe, 0x5c, 0x60, 0xfe, 0xd7, 0x60, 0xfe, 0xff, 0xa4, 0xfe, 0xff, 0xea, 0xfe, 0xd3, 0x0c, 0xff, 0x00, 0xc7, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0xfe, 0x30, 0xc8, 0xfe, 0xef, 0xea, 0xfe, 0xf0, 0xe9, 0xfe, 0x6b, 0xe9, 0xfe, 0x10, 0xc8, 0xfe, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xea, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x03, 0x60, 0xfe, 0x14, 0x60, 0xfe, 0x6f, 0x60, 0xfe, 0xef, 0xc7, 0xfe, 0xff, 0xc9, 0xfe, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc7, 0xfe, 0x4b, 0xc8, 0xfe, 0x70, 0xc7, 0xfe, 0x10, 0xc8, 0xfe, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe9, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x60, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xfe, 0x00, 0x40, 0xfe, 0x10, 0x61, 0xfe, 0x90, 0xa4, 0xfe, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format:  Blue: 5 bit Green: 6 bit, Red: 5 bit, Alpha 8 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x84, 0x54, 0xfe, 0xc8, 0x23, 0x00, 0x00, 0x00, 0xfe, 0xea, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x62, 0xc7, 0xfe, 0xe9, 0xac, 0xfe, 0xa6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x50, 0xfe, 0x61, 0xec, 0xfe, 0xea, 0xff, 0xfe, 0xea, 0x2c, 0x00, 0x00, 0x00, 0xfe, 0xea, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x03, 0xfe, 0x60, 0xaf, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xea, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x4b, 0xfe, 0x60, 0xe8, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0x34, 0x00, 0x00, 0x00, 0xfe, 0xea, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x08, 0xfe, 0x60, 0x9c, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xb7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x3f, 0xfe, 0x60, 0xe8, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xf0, 0xfe, 0xea, 0x3f, 0x00, 0x00, 0x00, 0xfe, 0xea, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x0b, 0xfe, 0x60, 0x8f, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x40, 0x2c, 0xfe, 0x60, 0xef, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xe4, 0xfe, 0xea, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x30, 0x07, 0xff, 0x30, 0x2f, 0xff, 0x2f, 0x5b, 0xfe, 0xc7, 0xb0, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xff, 0xff, 0x0c, 0xc4, 0xff, 0x51, 0x5b, 0xff, 0x30, 0x34, 0xff, 0x30, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x30, 0x00,
  0xfe, 0xc6, 0x08, 0xff, 0x0d, 0x18, 0xff, 0x30, 0x3f, 0xff, 0x30, 0x68, 0xff, 0x30, 0x93, 0xff, 0x30, 0xbc, 0xff, 0x30, 0xe7, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x2e, 0xff, 0xfe, 0x84, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xeb, 0xff, 0x30, 0xc3, 0xff, 0x30, 0x98, 0xff, 0x30, 0x6f, 0xff, 0x30, 0x44, 0xff, 0x2e, 0x1c, 0xfe, 0xc7, 0x07,
  0xfe, 0xa6, 0x87, 0xfe, 0xea, 0xff, 0xff, 0x2e, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x50, 0xff, 0xff, 0x0c, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xff, 0x0e, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x0e, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xc7, 0x83,
  0x00, 0x00, 0x00, 0xfe, 0x40, 0x93, 0xfe, 0x60, 0xfb, 0xfe, 0xa4, 0xff, 0xfe, 0xe9, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x2e, 0xff, 0xff, 0x30, 0xff, 0xff, 0x50, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xfe, 0xc8, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x50, 0xff, 0xff, 0x30, 0xff, 0xff, 0x30, 0xff, 0xff, 0x50, 0xff, 0xff, 0x30, 0xff, 0xff, 0x2e, 0xff, 0xff, 0x0d, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0x60, 0xfb, 0xfe, 0x40, 0x8f, 0xfe, 0x00, 0x0c,
  0x00, 0x00, 0x00, 0xfe, 0x60, 0x03, 0xfe, 0x60, 0x8b, 0xfe, 0x60, 0xfb, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xc9, 0xff, 0xff, 0x0c, 0xff, 0xff, 0x2e, 0xff, 0xff, 0x30, 0xff, 0xff, 0x51, 0xff, 0xff, 0x2f, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xea, 0xff, 0xff, 0x0c, 0xff, 0xff, 0x50, 0xff, 0xff, 0x50, 0xff, 0xff, 0x50, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x0c, 0xff, 0xfe, 0xe9, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x40, 0xff, 0xfe, 0x60, 0xfc, 0xfe, 0x60, 0x8c, 0xfe, 0x60, 0x0b, 0x00, 0x00, 0x00,
  0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x08, 0xfe, 0x60, 0x87, 0xfe, 0x60, 0xf8, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x40, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xa4, 0xff, 0xfe, 0xc7, 0xff, 0xfe, 0xeb, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x0d, 0xff, 0xfe, 0x83, 0xff, 0xfe, 0xeb, 0xff, 0xff, 0x30, 0xff, 0xff, 0x2f, 0xff, 0xff, 0x0c, 0xff, 0xfe, 0xc8, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0x81, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0x88, 0xfe, 0x60, 0x08, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00,
  0xfe, 0x60, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x0b, 0xfe, 0x60, 0x84, 0xfe, 0x60, 0xfb, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x83, 0xff, 0xfe, 0xc6, 0xff, 0xfe, 0xa6, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xc7, 0xff, 0xfe, 0x84, 0xff, 0xfe, 0x82, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0x7f, 0xfe, 0x60, 0x07, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x07, 0xfe, 0x60, 0x84, 0xfe, 0x60, 0xfc, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x82, 0xff, 0xfe, 0x83, 0xff, 0xfe, 0xc7, 0xff, 0xfe, 0xa6, 0xff, 0xfe, 0xc7, 0xff, 0xfe, 0x84, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0x73, 0xfe, 0x60, 0x07, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x03, 0xfe, 0x60, 0x83, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x82, 0xff, 0xfe, 0x84, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xa7, 0xff, 0xfe, 0xe9, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xc9, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xe9, 0xff, 0xfe, 0xc8, 0xff, 0xfe, 0xa4, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0x6c, 0xfe, 0x60, 0x07, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x84, 0xb4, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xa6, 0xff, 0xfe, 0xe9, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x81, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xc7, 0x8b, 0xfe, 0x83, 0x0b, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0xc8, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xc8, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x84, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0x97, 0xfe, 0xeb, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0xef, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xc7, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0xa7, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xc0, 0xfe, 0xeb, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0x20, 0xfe, 0xa5, 0xf4, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xa6, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x81, 0xff, 0xfe, 0xc9, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xe8, 0xfe, 0xeb, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0x4b, 0xfe, 0xa5, 0xf7, 0xfe, 0xa5, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xc9, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x83, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0x1f, 0xfe, 0xe9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0x77, 0xfe, 0xa5, 0xfb, 0xfe, 0xa5, 0xff, 0xfe, 0xc8, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xef, 0xfe, 0x61, 0xf8, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0xa5, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0x47, 0xfe, 0xea, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0xa3, 0xfe, 0xa5, 0xfc, 0xfe, 0xa6, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xfc, 0xfe, 0xeb, 0xb3, 0xfe, 0xea, 0x58, 0xfe, 0x60, 0x5c, 0xfe, 0x60, 0xb0, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x61, 0xff, 0xfe, 0xc8, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0x74, 0xfe, 0xea, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0xcb, 0xfe, 0xa6, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xc0, 0xfe, 0xea, 0x5b, 0xfe, 0xea, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x1b, 0xfe, 0x60, 0x58, 0xfe, 0x60, 0xbf, 0xfe, 0x60, 0xff, 0xfe, 0x60, 0xff, 0xfe, 0x82, 0xff, 0xfe, 0xea, 0xff, 0xfe, 0xeb, 0xa4, 0xfe, 0xea, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0x0b, 0xfe, 0xa5, 0xdc, 0xfe, 0xc9, 0xff, 0xfe, 0xeb, 0xff, 0xfe, 0xea, 0xdb, 0xfe, 0xea, 0x5c, 0xfe, 0xea, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xa5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x1b, 0xfe, 0x60, 0x5c, 0xfe, 0x60, 0xd7, 0xfe, 0x60, 0xff, 0xfe, 0xa4, 0xff, 0xfe, 0xea, 0xd3, 0xff, 0x0c, 0x00, 0xfe, 0xc7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x84, 0x30, 0xfe, 0xc8, 0xef, 0xfe, 0xea, 0xf0, 0xfe, 0xe9, 0x6b, 0xfe, 0xe9, 0x10, 0xfe, 0xc8, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xea, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x03, 0xfe, 0x60, 0x14, 0xfe, 0x60, 0x6f, 0xfe, 0x60, 0xef, 0xfe, 0xc7, 0xff, 0xfe, 0xc9, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xc7, 0x4b, 0xfe, 0xc8, 0x70, 0xfe, 0xc7, 0x10, 0xfe, 0xc8, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xe9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0xfe, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x60, 0x00, 0xfe, 0x40, 0x10, 0xfe, 0x61, 0x90, 0xfe, 0xa4, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
#endif
#if LV_COLOR_DEPTH == 32
  /*Pixel format:  Blue: 8 bit, Green: 8 bit, Red: 8 bit, Alpha: 8 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1d, 0xd2, 0xff, 0x54, 0x43, 0xd9, 0xff, 0x23, 0x00, 0x00, 0x00, 0x00, 0x4d, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0xce, 0xff, 0xc7, 0x4c, 0xdb, 0xff, 0xac, 0x31, 0xd6, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcb, 0xff, 0x50, 0x08, 0xce, 0xff, 0xec, 0x4f, 0xdc, 0xff, 0xff, 0x50, 0xdb, 0xff, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x4d, 0xdc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x03, 0x00, 0xcc, 0xff, 0xaf, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x54, 0xdc, 0xff, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x4b, 0x00, 0xcc, 0xff, 0xe8, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x51, 0xdb, 0xff, 0x34, 0x00, 0x00, 0x00, 0x00, 0x4e, 0xdc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x08, 0x00, 0xcc, 0xff, 0x9c, 0x00, 0xcc, 0xff, 0xff, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x54, 0xdd, 0xff, 0xb7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x3f, 0x00, 0xcc, 0xff, 0xe8, 0x00, 0xcc, 0xff, 0xff, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xf0, 0x51, 0xdd, 0xff, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x4f, 0xdc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x0b, 0x00, 0xcc, 0xff, 0x8f, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xe6, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xca, 0xff, 0x2c, 0x00, 0xcc, 0xff, 0xef, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xe4, 0x51, 0xdd, 0xff, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xe6, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xe6, 0xff, 0x07, 0x80, 0xe6, 0xff, 0x2f, 0x7b, 0xe5, 0xff, 0x5b, 0x36, 0xd7, 0xff, 0xb0, 0x00, 0xcb, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x54, 0xdd, 0xff, 0xff, 0x5e, 0xdf, 0xff, 0xc4, 0x87, 0xe8, 0xff, 0x5b, 0x80, 0xe6, 0xff, 0x34, 0x80, 0xe6, 0xff, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xe6, 0xff, 0x00,
  0x33, 0xd7, 0xff, 0x08, 0x6c, 0xe2, 0xff, 0x18, 0x81, 0xe6, 0xff, 0x3f, 0x81, 0xe6, 0xff, 0x68, 0x80, 0xe6, 0xff, 0x93, 0x80, 0xe6, 0xff, 0xbc, 0x80, 0xe6, 0xff, 0xe7, 0x80, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x71, 0xe3, 0xff, 0xff, 0x1e, 0xd2, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x5b, 0xde, 0xff, 0xff, 0x78, 0xe5, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xeb, 0x80, 0xe6, 0xff, 0xc3, 0x80, 0xe6, 0xff, 0x98, 0x81, 0xe6, 0xff, 0x6f, 0x81, 0xe6, 0xff, 0x44, 0x71, 0xe3, 0xff, 0x1c, 0x35, 0xd7, 0xff, 0x07,
  0x31, 0xd6, 0xff, 0x87, 0x50, 0xdc, 0xff, 0xff, 0x6e, 0xe3, 0xff, 0xff, 0x77, 0xe4, 0xff, 0xff, 0x7e, 0xe6, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x83, 0xe7, 0xff, 0xff, 0x5e, 0xdf, 0xff, 0xff, 0x0a, 0xce, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x57, 0xdd, 0xff, 0xff, 0x6e, 0xe2, 0xff, 0xff, 0x83, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x7f, 0xe6, 0xff, 0xff, 0x78, 0xe4, 0xff, 0xff, 0x6e, 0xe2, 0xff, 0xff, 0x52, 0xdd, 0xff, 0xff, 0x38, 0xd8, 0xff, 0x83,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xca, 0xff, 0x93, 0x01, 0xcc, 0xff, 0xfb, 0x24, 0xd4, 0xff, 0xff, 0x4b, 0xdc, 0xff, 0xff, 0x65, 0xe1, 0xff, 0xff, 0x72, 0xe3, 0xff, 0xff, 0x7f, 0xe6, 0xff, 0xff, 0x83, 0xe7, 0xff, 0xff, 0x82, 0xe6, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x43, 0xda, 0xff, 0xff, 0x01, 0xcc, 0xff, 0xff, 0x07, 0xcd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x66, 0xe1, 0xff, 0xff, 0x82, 0xe7, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x83, 0xe7, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x74, 0xe4, 0xff, 0xff, 0x67, 0xe1, 0xff, 0xff, 0x4f, 0xdc, 0xff, 0xff, 0x27, 0xd4, 0xff, 0xff, 0x04, 0xcd, 0xff, 0xfb, 0x00, 0xc9, 0xff, 0x8f, 0x00, 0xc1, 0xff, 0x0c,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x03, 0x00, 0xcc, 0xff, 0x8b, 0x00, 0xcb, 0xff, 0xfb, 0x00, 0xcb, 0xff, 0xff, 0x06, 0xcd, 0xff, 0xff, 0x25, 0xd3, 0xff, 0xff, 0x45, 0xda, 0xff, 0xff, 0x5d, 0xdf, 0xff, 0xff, 0x70, 0xe3, 0xff, 0xff, 0x81, 0xe6, 0xff, 0xff, 0x87, 0xe7, 0xff, 0xff, 0x7a, 0xe5, 0xff, 0xff, 0x27, 0xd4, 0xff, 0xff, 0x07, 0xcd, 0xff, 0xff, 0x4f, 0xdc, 0xff, 0xff, 0x5e, 0xdf, 0xff, 0xff, 0x80, 0xe7, 0xff, 0xff, 0x84, 0xe7, 0xff, 0xff, 0x83, 0xe7, 0xff, 0xff, 0x75, 0xe4, 0xff, 0xff, 0x61, 0xe0, 0xff, 0xff, 0x4a, 0xdb, 0xff, 0xff, 0x2a, 0xd5, 0xff, 0xff, 0x09, 0xce, 0xff, 0xff, 0x00, 0xca, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xfc, 0x00, 0xcc, 0xff, 0x8c, 0x02, 0xcd, 0xff, 0x0b, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x08, 0x00, 0xcc, 0xff, 0x87, 0x00, 0xcc, 0xff, 0xf8, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x00, 0xca, 0xff, 0xff, 0x08, 0xce, 0xff, 0xff, 0x22, 0xd3, 0xff, 0xff, 0x3c, 0xd8, 0xff, 0xff, 0x55, 0xde, 0xff, 0xff, 0x75, 0xe4, 0xff, 0xff, 0x6b, 0xe2, 0xff, 0xff, 0x1c, 0xd2, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x80, 0xe6, 0xff, 0xff, 0x78, 0xe4, 0xff, 0xff, 0x5d, 0xdf, 0xff, 0xff, 0x42, 0xda, 0xff, 0xff, 0x28, 0xd4, 0xff, 0xff, 0x0c, 0xcf, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0x88, 0x00, 0xcc, 0xff, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00,
  0x00, 0xcc, 0xff, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x0b, 0x00, 0xcc, 0xff, 0x84, 0x00, 0xcc, 0xff, 0xfb, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x09, 0xce, 0xff, 0xff, 0x1b, 0xd2, 0xff, 0xff, 0x34, 0xd7, 0xff, 0xff, 0x2e, 0xd5, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x37, 0xd7, 0xff, 0xff, 0x1f, 0xd2, 0xff, 0xff, 0x0e, 0xcf, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0x7f, 0x00, 0xcc, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x07, 0x00, 0xcc, 0xff, 0x84, 0x00, 0xcc, 0xff, 0xfc, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x02, 0xcd, 0xff, 0xff, 0x0d, 0xcf, 0xff, 0xff, 0x19, 0xd1, 0xff, 0xff, 0x39, 0xd8, 0xff, 0xff, 0x34, 0xd6, 0xff, 0xff, 0x3c, 0xd9, 0xff, 0xff, 0x1d, 0xd2, 0xff, 0xff, 0x07, 0xce, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x00, 0xcb, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0x73, 0x00, 0xcc, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x03, 0x00, 0xcc, 0xff, 0x83, 0x00, 0xcc, 0xff, 0xff, 0x02, 0xcc, 0xff, 0xff, 0x10, 0xd0, 0xff, 0xff, 0x1d, 0xd2, 0xff, 0xff, 0x26, 0xd3, 0xff, 0xff, 0x26, 0xd3, 0xff, 0xff, 0x38, 0xd6, 0xff, 0xff, 0x4a, 0xdb, 0xff, 0xff, 0x08, 0xcd, 0xff, 0xff, 0x46, 0xda, 0xff, 0xff, 0x52, 0xdc, 0xff, 0xff, 0x4b, 0xdb, 0xff, 0xff, 0x3d, 0xd8, 0xff, 0xff, 0x20, 0xd3, 0xff, 0xff, 0x05, 0xcd, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0x6c, 0x00, 0xcc, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1d, 0xd1, 0xff, 0xb4, 0x28, 0xd4, 0xff, 0xff, 0x29, 0xd4, 0xff, 0xff, 0x2a, 0xd4, 0xff, 0xff, 0x29, 0xd4, 0xff, 0xff, 0x2f, 0xd5, 0xff, 0xff, 0x4c, 0xdb, 0xff, 0xff, 0x4f, 0xdc, 0xff, 0xff, 0x05, 0xcd, 0xff, 0xff, 0x0c, 0xcf, 0xff, 0xff, 0x54, 0xdd, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x53, 0xdd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x39, 0xd8, 0xff, 0x8b, 0x1c, 0xd2, 0xff, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xd4, 0xff, 0xc8, 0x2a, 0xd4, 0xff, 0xff, 0x2a, 0xd4, 0xff, 0xff, 0x2a, 0xd4, 0xff, 0xff, 0x2a, 0xd4, 0xff, 0xff, 0x42, 0xd9, 0xff, 0xff, 0x57, 0xdd, 0xff, 0xff, 0x4f, 0xdc, 0xff, 0xff, 0x05, 0xcd, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x1e, 0xd2, 0xff, 0xff, 0x59, 0xde, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0x97, 0x55, 0xdd, 0xff, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xd4, 0xff, 0xef, 0x2a, 0xd4, 0xff, 0xff, 0x2a, 0xd4, 0xff, 0xff, 0x2a, 0xd4, 0xff, 0xff, 0x39, 0xd7, 0xff, 0xff, 0x53, 0xdd, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x4e, 0xdc, 0xff, 0xff, 0x05, 0xcd, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x02, 0xcc, 0xff, 0xff, 0x35, 0xd6, 0xff, 0xff, 0x57, 0xde, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xc0, 0x55, 0xdd, 0xff, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xd4, 0xff, 0x20, 0x2a, 0xd4, 0xff, 0xf4, 0x2a, 0xd4, 0xff, 0xff, 0x2a, 0xd4, 0xff, 0xff, 0x31, 0xd6, 0xff, 0xff, 0x4d, 0xdc, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x4e, 0xdc, 0xff, 0xff, 0x05, 0xcd, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x0a, 0xcf, 0xff, 0xff, 0x46, 0xda, 0xff, 0xff, 0x57, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xe8, 0x55, 0xdd, 0xff, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xd4, 0xff, 0x4b, 0x2a, 0xd4, 0xff, 0xf7, 0x2a, 0xd4, 0xff, 0xff, 0x2c, 0xd5, 0xff, 0xff, 0x45, 0xda, 0xff, 0xff, 0x58, 0xde, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x4e, 0xdc, 0xff, 0xff, 0x05, 0xcd, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x19, 0xd1, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x53, 0xdd, 0xff, 0x1f, 0x4a, 0xdc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xd4, 0xff, 0x77, 0x2a, 0xd4, 0xff, 0xfb, 0x29, 0xd4, 0xff, 0xff, 0x3d, 0xd8, 0xff, 0xff, 0x57, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x50, 0xdc, 0xff, 0xef, 0x05, 0xcd, 0xff, 0xf8, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x01, 0xcc, 0xff, 0xff, 0x2c, 0xd5, 0xff, 0xff, 0x54, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x53, 0xdd, 0xff, 0x47, 0x52, 0xdd, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xd4, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xd4, 0xff, 0xa3, 0x29, 0xd4, 0xff, 0xfc, 0x34, 0xd6, 0xff, 0xff, 0x53, 0xdd, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xfc, 0x55, 0xdd, 0xff, 0xb3, 0x53, 0xdd, 0xff, 0x58, 0x04, 0xcd, 0xff, 0x5c, 0x00, 0xcc, 0xff, 0xb0, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x08, 0xce, 0xff, 0xff, 0x3e, 0xd8, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x54, 0xdd, 0xff, 0x74, 0x54, 0xdd, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xd4, 0xff, 0xcb, 0x2d, 0xd5, 0xff, 0xff, 0x4e, 0xdc, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xff, 0x53, 0xdc, 0xff, 0xc0, 0x53, 0xdd, 0xff, 0x5b, 0x53, 0xdd, 0xff, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x1b, 0x00, 0xcc, 0xff, 0x58, 0x00, 0xcc, 0xff, 0xbf, 0x00, 0xcc, 0xff, 0xff, 0x00, 0xcc, 0xff, 0xff, 0x12, 0xd0, 0xff, 0xff, 0x4d, 0xdc, 0xff, 0xff, 0x55, 0xdd, 0xff, 0xa4, 0x54, 0xdd, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xd3, 0xff, 0x0b, 0x27, 0xd4, 0xff, 0xdc, 0x47, 0xda, 0xff, 0xff, 0x56, 0xdd, 0xff, 0xff, 0x51, 0xdc, 0xff, 0xdb, 0x4e, 0xdc, 0xff, 0x5c, 0x52, 0xdc, 0xff, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0xd4, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x1b, 0x00, 0xcc, 0xff, 0x5c, 0x00, 0xcc, 0xff, 0xd7, 0x00, 0xcc, 0xff, 0xff, 0x24, 0xd3, 0xff, 0xff, 0x53, 0xdd, 0xff, 0xd3, 0x5d, 0xdf, 0xff, 0x00, 0x39, 0xd7, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0xd1, 0xff, 0x30, 0x41, 0xd9, 0xff, 0xef, 0x4f, 0xdc, 0xff, 0xf0, 0x48, 0xdb, 0xff, 0x6b, 0x4a, 0xdb, 0xff, 0x10, 0x3f, 0xda, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xdc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x03, 0x00, 0xcc, 0xff, 0x14, 0x00, 0xcc, 0xff, 0x6f, 0x00, 0xcc, 0xff, 0xef, 0x37, 0xd7, 0xff, 0xff, 0x49, 0xda, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xd7, 0xff, 0x4b, 0x40, 0xd9, 0xff, 0x70, 0x3a, 0xd8, 0xff, 0x10, 0x43, 0xda, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xdb, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0xcc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xcc, 0xff, 0x00, 0x00, 0xca, 0xff, 0x10, 0x0c, 0xce, 0xff, 0x90, 0x20, 0xd3, 0xff, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
#endif
};

const lv_img_dsc_t img_star = {
  .header.always_zero = 0,
  .header.w = 30,
  .header.h = 29,
  .data_size = 870 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = img_star_map,
};

#endif /* LV_BUILD_EXAMPLES */
