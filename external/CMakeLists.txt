# External dependencies configuration (stable alias variables)

# Stable paths to versioned directories; adjust here on version bump
set(AMBIQSUITE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/AmbiqSuite_R4.5.0 CACHE PATH "AmbiqSuite root")
set(FREERTOS_DIR  ${CMAKE_CURRENT_SOURCE_DIR}/FreeRTOSv10.5.1 CACHE PATH "FreeRTOS root")
# Prefer lowercase lvgl if present for style; otherwise fallback to LVGL
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lvgl)
  set(LVGL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/lvgl CACHE PATH "LVGL root")
else()
  set(LVGL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/LVGL CACHE PATH "LVGL root")
endif()
set(THINKSI_DIR   ${CMAKE_CURRENT_SOURCE_DIR}/ThinkSi CACHE PATH "ThinkSi root")

# -------------------------------------------------------------------------------------------------
# LVGL (upstream CMake) + simple include macros
# -------------------------------------------------------------------------------------------------
add_subdirectory(${LVGL_DIR}/lvgl)
# Ensure lvgl finds lv_conf.h in project tree when included as "lv_conf.h"
target_compile_definitions(lvgl PUBLIC LV_CONF_INCLUDE_SIMPLE LV_LVGL_H_INCLUDE_SIMPLE)
target_include_directories(lvgl PUBLIC ${CMAKE_SOURCE_DIR}/src ${LVGL_DIR}/lvgl)
# Ensure lvgl_examples also sees lv_conf.h and lvgl root
if(TARGET lvgl_examples)
  target_compile_definitions(lvgl_examples PUBLIC LV_CONF_INCLUDE_SIMPLE LV_LVGL_H_INCLUDE_SIMPLE)
  target_include_directories(lvgl_examples PUBLIC ${CMAKE_SOURCE_DIR}/src ${LVGL_DIR}/lvgl)
endif()

# Ensure lvgl core sees Ambiq/LVGL/ThinkSi headers
if(TARGET lvgl)
  target_link_libraries(lvgl PUBLIC ambiqsuite_includes)
endif()
# Propagate Ambiq framebuffer config defines to lvgl core (needed by lv_gpu_ambiq_nema.c)
if(TARGET lvgl)
  target_compile_definitions(lvgl PUBLIC
    LV_AMBIQ_FB_RESX=390
    LV_AMBIQ_FB_RESY=390
    LV_AMBIQ_FB_USE_RGB565=1
    LV_AMBIQ_FB_USE_RGB888=0)
endif()



# -------------------------------------------------------------------------------------------------
# FreeRTOS kernel (Apollo4/GCC) as a static library
# -------------------------------------------------------------------------------------------------
set(FREERTOS_CONFIG_DIR "${CMAKE_SOURCE_DIR}/src" CACHE PATH "Dir containing FreeRTOSConfig.h")
add_library(freertos STATIC
  ${FREERTOS_DIR}/Source/event_groups.c
  ${FREERTOS_DIR}/Source/list.c
  ${FREERTOS_DIR}/Source/queue.c
  ${FREERTOS_DIR}/Source/stream_buffer.c
  ${FREERTOS_DIR}/Source/tasks.c
  ${FREERTOS_DIR}/Source/timers.c
  ${FREERTOS_DIR}/Source/portable/GCC/AMapollo4/port.c
  ${FREERTOS_DIR}/Source/portable/MemMang/heap_4.c
)
# Public includes for FreeRTOS and Apollo4 port; private include for FreeRTOSConfig.h
target_include_directories(freertos PUBLIC
  ${FREERTOS_DIR}/Source/include
  ${FREERTOS_DIR}/Source/portable/GCC/AMapollo4
)
# FreeRTOSConfig.h lives in the application tree by default
target_include_directories(freertos PRIVATE ${FREERTOS_CONFIG_DIR})

# -------------------------------------------------------------------------------------------------
# Ambiq HAL and BSP: rebuild via vendor Makefiles and import resulting libs
# Compose extra include flags for vendor Makefiles (our tree differs from original paths)
set(HAL_EXTRA_CFLAGS "-I${AMBIQSUITE_DIR}/ARM/Include -I${AMBIQSUITE_DIR}/AmbiqMicro/Include -I${AMBIQSUITE_DIR}/mcu/apollo4p -I${AMBIQSUITE_DIR}/mcu/apollo4p/hal -I${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu -I${AMBIQSUITE_DIR}/utils")
set(BSP_EXTRA_CFLAGS "-I${AMBIQSUITE_DIR}/ARM/Include -I${AMBIQSUITE_DIR}/AmbiqMicro/Include -I${AMBIQSUITE_DIR}/devices -I${AMBIQSUITE_DIR}/mcu/apollo4p -I${AMBIQSUITE_DIR}/mcu/apollo4p/hal -I${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu -I${AMBIQSUITE_DIR}/utils -I${THINKSI_DIR}/NemaGFX_SDK -I${THINKSI_DIR}/NemaGFX_SDK/NemaDC -I${THINKSI_DIR}/NemaGFX_SDK/NemaGFX -I${THINKSI_DIR}/NemaGFX_SDK/common/mem -I${THINKSI_DIR}/NemaGFX_SDK/include/tsi/NemaDC -I${THINKSI_DIR}/NemaGFX_SDK/include/tsi/NemaGFX -I${THINKSI_DIR}/NemaGFX_SDK/include/tsi/NemaVG -I${THINKSI_DIR}/NemaGFX_SDK/include/tsi/common -I${THINKSI_DIR}/config/apollo4p_nemagfx")

# -------------------------------------------------------------------------------------------------
add_custom_target(build_am_hal
  COMMAND ${CMAKE_MAKE_PROGRAM} -C ${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu/gcc "EXTRA_CFLAGS=${HAL_EXTRA_CFLAGS}"
  BYPRODUCTS ${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a
  COMMENT "Building Ambiq HAL library (vendor Makefile)"
)
add_custom_target(build_am_bsp
  COMMAND ${CMAKE_MAKE_PROGRAM} -C ${AMBIQSUITE_DIR}/bsp/gcc "EXTRA_CFLAGS=${BSP_EXTRA_CFLAGS}"
  BYPRODUCTS ${AMBIQSUITE_DIR}/bsp/gcc/bin/libam_bsp.a
  COMMENT "Building Ambiq BSP library (vendor Makefile)"
)

add_library(am_hal STATIC IMPORTED)
set_target_properties(am_hal PROPERTIES
  IMPORTED_LOCATION ${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a
  IMPORTED_LOCATION_RELEASE ${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu/gcc/bin/libam_hal.a
)
add_dependencies(am_hal build_am_hal)

add_library(am_bsp STATIC IMPORTED)
set_target_properties(am_bsp PROPERTIES
  IMPORTED_LOCATION ${AMBIQSUITE_DIR}/bsp/gcc/bin/libam_bsp.a
  IMPORTED_LOCATION_RELEASE ${AMBIQSUITE_DIR}/bsp/gcc/bin/libam_bsp.a
  INTERFACE_LINK_LIBRARIES am_hal
)
add_dependencies(am_bsp build_am_bsp)

# -------------------------------------------------------------------------------------------------
# LVGL Ambiq GPU porting lib: rebuild via Makefile and import
# -------------------------------------------------------------------------------------------------
add_library(lvgl_ambiq_porting STATIC IMPORTED)
set_target_properties(lvgl_ambiq_porting PROPERTIES
  IMPORTED_LOCATION ${LVGL_DIR}/ambiq_support/gpu_lib_apollo4/gcc/bin/lvgl_ambiq_porting.a
  IMPORTED_LOCATION_RELEASE ${LVGL_DIR}/ambiq_support/gpu_lib_apollo4/gcc/bin/lvgl_ambiq_porting.a
)

# -------------------------------------------------------------------------------------------------
# ThinkSi NemaGFX prebuilt lib (imported). Building full SDK from source is non-trivial.
# -------------------------------------------------------------------------------------------------
add_library(nema_apollo4p STATIC IMPORTED)
set_target_properties(nema_apollo4p PROPERTIES
  IMPORTED_LOCATION ${THINKSI_DIR}/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a
  IMPORTED_LOCATION_RELEASE ${THINKSI_DIR}/config/apollo4p_nemagfx/gcc/bin/lib_nema_apollo4p_nemagfx.a
)

# -------------------------------------------------------------------------------------------------
# CMSIS DSP math library from AmbiqSuite
# -------------------------------------------------------------------------------------------------
add_library(arm_math STATIC IMPORTED)
set_target_properties(arm_math PROPERTIES
  IMPORTED_LOCATION ${AMBIQSUITE_DIR}/ARM/Lib/ARM/libarm_cortexM4lf_math.a
  IMPORTED_LOCATION_RELEASE ${AMBIQSUITE_DIR}/ARM/Lib/ARM/libarm_cortexM4lf_math.a
)

# -------------------------------------------------------------------------------------------------
# Centralized include interface for Ambiq/LVGL/ThinkSi
# -------------------------------------------------------------------------------------------------
add_library(ambiqsuite_includes INTERFACE)
target_include_directories(ambiqsuite_includes INTERFACE
  ${AMBIQSUITE_DIR}/ARM/Include
  ${AMBIQSUITE_DIR}/AmbiqMicro/Include
  ${AMBIQSUITE_DIR}/devices
  ${AMBIQSUITE_DIR}/mcu/apollo4p
  ${AMBIQSUITE_DIR}/mcu/apollo4p/hal
  ${AMBIQSUITE_DIR}/mcu/apollo4p/hal/mcu
  ${AMBIQSUITE_DIR}/utils
  ${AMBIQSUITE_DIR}/bsp

  # LVGL core and Ambiq support headers
  ${LVGL_DIR}/ambiq_support
  ${LVGL_DIR}/lvgl
  ${LVGL_DIR}/lvgl/src/gpu

  # ThinkSi SDK headers and board config (for nema_sys_defs.h)
  ${THINKSI_DIR}/NemaGFX_SDK
  ${THINKSI_DIR}/NemaGFX_SDK/include/tsi/NemaDC
  ${THINKSI_DIR}/NemaGFX_SDK/include/tsi/NemaGFX
  ${THINKSI_DIR}/NemaGFX_SDK/include/tsi/NemaVG
  ${THINKSI_DIR}/NemaGFX_SDK/include/tsi/common
  ${THINKSI_DIR}/NemaGFX_SDK/NemaGFX
  ${THINKSI_DIR}/NemaGFX_SDK/NemaDC
  ${THINKSI_DIR}/config/apollo4p_nemagfx

  # FreeRTOS kernel public and port headers
  ${FREERTOS_DIR}/Source/include
  ${FREERTOS_DIR}/Source/portable/GCC/AMapollo4
)

# -------------------------------------------------------------------------------------------------
# LVGL ambiq_support shim library (the upstream LVGL CMake does not include these)
# -------------------------------------------------------------------------------------------------
# Make Ambiq headers visible to the FreeRTOS port sources
if(TARGET freertos)
  target_link_libraries(freertos PUBLIC ambiqsuite_includes)
endif()

add_library(lvgl_ambiq_support STATIC
  ${LVGL_DIR}/ambiq_support/lv_ambiq_decoder.c
  ${LVGL_DIR}/ambiq_support/lv_ambiq_font_align.c
  ${LVGL_DIR}/ambiq_support/lv_ambiq_fs.c
  ${LVGL_DIR}/ambiq_support/lv_ambiq_misc.c
  ${LVGL_DIR}/ambiq_support/lv_ambiq_nema_hal.c
  ${LVGL_DIR}/ambiq_support/lv_ambiq_touch.c
  ${LVGL_DIR}/ambiq_support/display_task_cpu_only.c
  ${LVGL_DIR}/ambiq_support/display_task_fake.c
  ${LVGL_DIR}/ambiq_support/display_task_one_and_partial_fb.c
  ${LVGL_DIR}/ambiq_support/display_task_one_fb.c
  ${LVGL_DIR}/ambiq_support/display_task_two_fb.c

)
target_link_libraries(lvgl_ambiq_support PUBLIC ambiqsuite_includes)
target_compile_definitions(lvgl_ambiq_support PUBLIC LV_CONF_INCLUDE_SIMPLE LV_LVGL_H_INCLUDE_SIMPLE)
target_include_directories(lvgl_ambiq_support PUBLIC ${CMAKE_SOURCE_DIR}/src ${LVGL_DIR}/lvgl)


# -------------------------------------------------------------------------------------------------
# Meta target: convenience for building vendor libs early
# -------------------------------------------------------------------------------------------------
add_custom_target(vendor_libs
  DEPENDS build_am_hal build_am_bsp
)
