Changes between V1.0.3 and V1.0.4 released

	+ Update to use stdint and the FreeRTOS specific typedefs that were
	  introduced in FreeRTOS V8.0.0.

Changes between V1.0.2 and V1.0.3 released

	+ Previously, and in line with good software engineering practice, the
	  FreeRTOS coding standard did not permit the use of char types that were
	  not explicitly qualified as either signed or unsigned. As a result char
	  pointers used to reference strings required casts, as did the use of any
	  standard string handling functions. The casts ensured compiler warnings
	  were not generated by compilers that defaulted unqualified char types to
	  be signed or compilers that defaulted unqualified char types to be
	  unsigned.  As it has in later MISRA standards, this rule has now been
	  relaxed, and unqualified char types are now permitted, but only when:
		1) The char is used to point to a human readable text string.
		2) The char is used to hold a single ASCII character.

Changes between V1.0.1 and V1.0.2 released 14/10/2013

	+ Changed double quotes (") to single quotes (') in the help string to
	  allow the strings to be used with JSON in FreeRTOS+Nabto.

Changes between V1.0.0 and V1.0.1 released 05/07/2012

	+ Change the name of the structure used to map a function that implements
	  a CLI command to the string used to call the command from
	  xCommandLineInput to CLI_Command_Definition_t, as it was always intended
	  to be.  A #define was added to map the old name to the new name for
	  reasons of backward compatibility.

